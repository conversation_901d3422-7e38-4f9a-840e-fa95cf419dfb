#include "OTA_VucServiceData.hpp"
#include "dds_service_manager/vuc_service_adapter.hpp"
#include "logger/logger.h"
#include <chrono>
#include <iostream>
#include <memory>
#include <signal.h>
#include <termios.h>
#include <thread>

using namespace seres::fotamaster;
using namespace seres::ota_vuc_service;

// 定义消息类型
struct TestMessage
{
    int32_t id;
    std::string content;
};

std::shared_ptr<seres::fotamaster::dds_wrapper::VucPublisher> g_publisher{nullptr};
std::shared_ptr<seres::fotamaster::dds_wrapper::VucSubscriber> g_subscriber{nullptr};

// 测试代码
static_assert(
    dds_wrapper::DdsTypeTraits<
        VucServiceDataUnion,
        VucServiceTopic
    >::template type_to_enum<UpgradeTask>()
    == VucServiceTopic::kUpgradeTaskNotify,
    "Type mapping failed for UpgradeTask");

template <typename T>
static constexpr bool IsSupportedType()
{
    // return std::is_same_v<T, decltype(dds_wrapper::DdsTypeTraits<VucServiceDataUnion,VucServiceTopic>::template get_value<T>(std::declval<VucServiceDataUnion>()))>;
    using Traits = dds_wrapper::DdsTypeTraits<VucServiceDataUnion, VucServiceTopic>;
    using ReturnType = decltype(Traits::template get_value<T>(std::declval<VucServiceDataUnion>()));

    // 移除引用和cv限定符进行比较
    return std::is_same_v<std::decay_t<ReturnType>, std::decay_t<T>>;
}

static_assert(IsSupportedType<UpgradeTask>(), "Unsupported message type");

static int create_subscriber()
{
    // 创建订阅者
    auto sub_result =
        seres::fotamaster::dds_wrapper::VucSubscriber::Create(kVucDomainID, kVucServiceTopicName);
    if (!sub_result)
    {
        std::cerr << "创建订阅者失败: " << sub_result.error_msg << std::endl;
        return -1;
    }
    g_subscriber = sub_result.GetValue();

    g_subscriber->Subscribe<seres::ota_vuc_service::UpgradeTask>(
        [](const seres::ota_vuc_service::UpgradeTask &msg) {
            std::cout << "收到消息: ID=" << msg.upgradeInfo()
                      << ", 内容=" << msg.upgradePackageInfo() << std::endl;
        });
    g_subscriber->Subscribe<seres::ota_vuc_service::TotalDownloadProgress>(
        [](const seres::ota_vuc_service::TotalDownloadProgress &msg) {
            std::cout << "收到消息: progress=" << msg.progress() << std::endl;
        });
    g_subscriber->Subscribe<seres::ota_vuc_service::SeamlessUpgradeMode>(
        [](const seres::ota_vuc_service::SeamlessUpgradeMode &msg) {
            std::cout << "收到消息: UseSeamlessUpgrade="
                      << static_cast<int32_t>(msg.use_seamless_upgrade())
                      << std::endl;
        });
    return 0;
}

#if 1
static int create_publisher()
{
    auto pub_result =
        dds_wrapper::VucPublisher::Create(kVucDomainID, kVucServiceTopicName);
    if (!pub_result)
    {
        std::cerr << "创建订阅者失败: " << pub_result.error_msg << std::endl;
        return -1;
    }
    g_publisher = pub_result.GetValue();
    return 0;
}

static void set_seamless_upgrade_mode()
{
    std::cout << "add seamless_upgrade_mode topic\n";
    // 等待连接建立
    std::cout << "等待连接建立..." << std::endl;
    while (/*!subscriber->IsConnected() ||*/ !g_publisher->IsConnected())
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    std::cout << "连接已建立, count: "
              << g_publisher->GetMatchedSubscribersCount().GetValue()
              << std::endl;

    SeamlessUpgradeMode msg1;
    msg1.use_seamless_upgrade(UseSeamlessUpgrade::kNo);
    auto result = g_publisher->Publish(msg1);
    if (!result)
    {
        std::cerr << "发布消息失败: err: " << result.error_msg << std::endl;
        return;
    }

    std::cout << "已发布消息: use_seamless_upgrade="
              << static_cast<uint32_t>(msg1.use_seamless_upgrade())
              << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(1));
}

static void upgrade_task_notify()
{
    // 等待连接建立
    std::cout << "等待连接建立..." << std::endl;
    while (/*!subscriber->IsConnected() ||*/ !g_publisher->IsConnected())
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    std::cout << "连接已建立, count: "
              << g_publisher->GetMatchedSubscribersCount().GetValue()
              << std::endl;

    // 发布消息
    for (int i = 0; i < 1; ++i)
    {
        seres::ota_vuc_service::UpgradeTask msg;
        msg.upgradeInfo("test1");
        msg.upgradePackageInfo("test2");
        auto result = g_publisher->Publish(msg);
        if (!result)
        {
            std::cerr << "发布消息失败: err: " << result.error_msg << std::endl;
            continue;
        }

        std::cout << "已发布消息: ID=" << msg.upgradeInfo()
                  << ", 内容=" << msg.upgradePackageInfo() << std::endl;

        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}

static void upgrade_mode_ctrl()
{
    std::cout << "add upgrade_mode_ctrl topic\n";
    // 等待连接建立
    std::cout << "等待连接建立..." << std::endl;
    while (/*!subscriber->IsConnected() ||*/ !g_publisher->IsConnected())
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    std::cout << "连接已建立, count: "
              << g_publisher->GetMatchedSubscribersCount().GetValue()
              << std::endl;

    seres::ota_vuc_service::UpgradeModeCtrl msg2;
    msg2.upgrade_mode(seres::ota_vuc_service::UpgradeModeE::kImmediately);
    msg2.time("20250523155000");
    auto result = g_publisher->Publish(msg2);
    if (!result)
    {
        std::cerr << "发布消息失败: err: " << result.error_msg << std::endl;
        return;
    }
    std::cout << "已发布消息: upgrade_mode="
              << static_cast<uint32_t>(msg2.upgrade_mode()) << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(1));
}

static void upgrade_notify()
{
    std::cout << "add UpgradeNotify topic\n";
    // 等待连接建立
    std::cout << "等待连接建立..." << std::endl;
    while (/*!subscriber->IsConnected() ||*/ !g_publisher->IsConnected())
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    std::cout << "连接已建立, count: "
              << g_publisher->GetMatchedSubscribersCount().GetValue()
              << std::endl;

    seres::ota_vuc_service::UpgradeNotify msg3;
    msg3.upgrade_notify(seres::ota_vuc_service::UpgradeNotifyE::kUpgrading);
    auto result = g_publisher->Publish(msg3);
    if (!result)
    {
        std::cerr << "发布消息失败: err: " << result.error_msg << std::endl;
        return;
    }
    std::cout << "已发布消息: upgrade_notify="
              << static_cast<uint32_t>(msg3.upgrade_notify()) << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(1));
}
#endif

// 设置终端为非规范模式（直接读取按键）
static void set_terminal_mode(bool enable_raw_mode)
{
    static struct termios original_tio;
    static bool is_original_saved = false;

    if (enable_raw_mode)
    {
        // 保存原始终端设置
        if (!is_original_saved)
        {
            tcgetattr(STDIN_FILENO, &original_tio);
            is_original_saved = true;
        }

        // 设置非规范模式：关闭回显、即时读取
        struct termios raw_tio = original_tio;
        raw_tio.c_lflag &= ~(ICANON | ECHO);
        tcsetattr(STDIN_FILENO, TCSANOW, &raw_tio);
    }
    else
    {
        // 恢复原始终端设置
        if (is_original_saved)
        {
            tcsetattr(STDIN_FILENO, TCSANOW, &original_tio);
        }
    }
}

static void press_space()
{
    set_terminal_mode(true);
    // 循环检测输入
    char c = 0;
    while (1)
    {
        if (read(STDIN_FILENO, &c, 1) == 1)
        {
            if (c == ' ')
            { // 空格键的 ASCII 码
                break;
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    set_terminal_mode(false);
}

// 信号处理函数
static void signal_handler(int sig)
{
    set_terminal_mode(false); // 恢复终端
    exit(sig);
}


int main()
{
    // 注册信号处理
    signal(SIGINT, signal_handler);  // Ctrl+C
    signal(SIGTERM, signal_handler); // kill

    LogConfig config;
    config.log_file_path = "logs/pub_test.log";  // 日志文件路径
    config.max_file_size = 5 * 1024 * 1024;      // 5MB
    config.max_files = 3;                        // 最多保留3个文件
    config.console_output = true;                // 输出到控制台
    config.log_level = LogLevel::kLogLevelDebug; // 日志级别

    // 初始化日志系统
    base::Singleton<Logger>::Instance().Init(config);
    LOG_INFO("hello pub test");

    if (create_subscriber() == -1)
    {
        return -1;
    }

    if (create_publisher() == -1)
    {
        return -1;
    }

    // 1
    press_space();
    set_seamless_upgrade_mode();

    // 2
    press_space();
    upgrade_task_notify();

    // 3
    press_space();
    upgrade_mode_ctrl();

    // 4
    press_space();
    upgrade_notify();

    // 等待所有消息被接收
    while (1)
    {
        std::this_thread::sleep_for(std::chrono::seconds(2));
    }

    std::cout << "测试完成" << std::endl;
    return 0;
}