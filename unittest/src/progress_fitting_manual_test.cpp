/**
 * @file progress_fitting_manual_test.cpp
 * @brief 进度拟合模块手动测试程序
 *
 * 本程序用于手动测试进度计算的准确性，验证权重分配和进度计算算法。
 */

#include "ev_loop/eventloop_manager.h"
#include "logger/logger.h"
#include "progress_fitting/progress_fitting.h"

#include <chrono>
#include <iomanip>
#include <iostream>
#include <thread>

using namespace seres::fotamaster;
using namespace seres::ota_duc_service;

class ProgressTestManager
{
public:
    ProgressTestManager()
    {
        // 初始化日志系统
        LogConfig logConfig;
        logConfig.console_output = true;
        logConfig.log_level = LogLevel::kLogLevelInfo;
        base::Singleton<Logger>::Instance().Init(logConfig);

        // 获取事件循环管理器（自动初始化）
        auto &eventLoopManager = base::Singleton<EventLoopManager>::Instance();
        m_eventLoop = eventLoopManager.GetDefaultLoop();

        // 初始化进度拟合模块
        m_progressFitting.Initialize(
            [this](uint8_t progress, bool allFinished) {
                this->onProgressUpdate(progress, allFinished);
            },
            0.5); // 500ms间隔
    }

    void onProgressUpdate(uint8_t progress, bool allFinished)
    {
        m_lastProgress = progress;
        m_allFinished = allFinished;

        std::cout << std::fixed << std::setprecision(1);
        std::cout << "onProgressUpdate callback [PROGRESS] "
                  << static_cast<int>(progress) << "%";
        if (allFinished)
        {
            std::cout << " (ALL FINISHED)";
        }
        std::cout << std::endl;
    }

    // 创建测试设备
    std::vector<InventoryInfo> createDevices(const std::string &prefix,
                                             int count)
    {
        std::vector<InventoryInfo> devices;
        for (int i = 1; i <= count; ++i)
        {
            InventoryInfo device;
            device.ecuName(prefix + "_ECU_" + std::to_string(i));
            device.serialNumber(prefix + "_" + std::to_string(i));
            device.partNumber(prefix + "_PART_" + std::to_string(i));
            devices.push_back(device);
        }
        return devices;
    }

    // 模拟设备进度更新（带详细日志）
    void simulateProgressWithDetails(DUCType ducType,
                                     const std::vector<std::string> &deviceIds,
                                     const std::vector<uint8_t> &progresses)
    {
        std::string domainName;
        switch (ducType)
        {
        case DUCType::CDC:
            domainName = "CDC";
            break;
        case DUCType::MDC:
            domainName = "MDC";
            break;
        case DUCType::ZCU:
            domainName = "ZCU";
            break;
        default:
            domainName = "UNKNOWN";
            break;
        }

        UpdateProgress updateProgress;
        bool allDevicesFinished = true; // 用于跟踪所有设备是否完成
        std::vector<DeviceUpdateProgress> progressList;
        std::cout << "  " << domainName << "域设备进度: ";

        for (size_t i = 0; i < deviceIds.size() && i < progresses.size(); ++i)
        {
            DeviceUpdateProgress deviceProgress;
            deviceProgress.deviceId(deviceIds[i]);
            deviceProgress.deviceName("Device_" + deviceIds[i]);
            deviceProgress.progressPercent(progresses[i]);
            deviceProgress.status(progresses[i] >= 100
                                      ? DeviceUpdateStatus::SUCCESS
                                      : DeviceUpdateStatus::UPDATING);

            // 如果有任何设备未达到100%，则标记为未全部完成
            if (progresses[i] < 100)
            {
                allDevicesFinished = false;
            }

            progressList.push_back(deviceProgress);

            std::cout << deviceIds[i] << "=" << static_cast<int>(progresses[i])
                      << "%";
            if (i < deviceIds.size() - 1)
                std::cout << ", ";
        }
        std::cout << std::endl;

        updateProgress.progressLists(progressList);
        updateProgress.allFinished(
            allDevicesFinished); // 根据所有设备的完成状态设置allFinished

        // 调用进度更新
        m_progressFitting.onUpdateProgress(ducType, updateProgress);
    }

    void runCompleteOTASimulation()
    {
        std::cout << "\n" << std::string(80, '=') << std::endl;
        std::cout << "完整OTA更新过程模拟 - 从0%到100%" << std::endl;
        std::cout << std::string(80, '=') << std::endl;

        // 重置进度拟合模块
        m_progressFitting.Reset();

        // 添加三个域的设备
        auto cdcDevices = createDevices("CDC", 2); // CDC域：2个设备
        auto mdcDevices = createDevices("MDC", 3); // MDC域：3个设备
        auto zcuDevices = createDevices("ZCU", 1); // ZCU域：1个设备

        m_progressFitting.AddUpdateDevice(DUCType::CDC, cdcDevices);
        m_progressFitting.AddUpdateDevice(DUCType::MDC, mdcDevices);
        m_progressFitting.AddUpdateDevice(DUCType::ZCU, zcuDevices);

        std::cout << "设备配置:" << std::endl;
        std::cout << "  CDC域: 2个设备 (CDC_1, CDC_2)" << std::endl;
        std::cout << "  MDC域: 3个设备 (MDC_1, MDC_2, MDC_3)" << std::endl;
        std::cout << "  ZCU域: 1个设备 (ZCU_1)" << std::endl;
        std::cout << std::string(80, '-') << std::endl;

        m_progressFitting.Start();

        // 模拟完整的更新过程，每10%一个步骤
        for (int step = 0; step <= 11; ++step)
        {
            int baseProgress = step * 10;

            std::cout << "\n[步骤 " << step + 1
                      << "/11] 模拟进度: " << baseProgress << "%" << std::endl;

            // CDC域设备进度 (稍微快一些)
            int cdc1_progress = std::min(100, baseProgress + 5);
            int cdc2_progress = std::min(100, baseProgress);

            // MDC域设备进度 (中等速度)
            int mdc1_progress = std::min(100, baseProgress);
            int mdc2_progress = std::min(100, baseProgress - 5);
            int mdc3_progress = std::min(100, baseProgress + 3);

            // ZCU域设备进度 (稍微慢一些)
            int zcu1_progress = std::min(100, baseProgress - 10);

            // 确保进度不为负数
            cdc2_progress = std::max(0, cdc2_progress);
            mdc2_progress = std::max(0, mdc2_progress);
            zcu1_progress = std::max(0, zcu1_progress);

            // 更新CDC域进度
            simulateProgressWithDetails(DUCType::CDC,
                                        {"CDC_1", "CDC_2"},
                                        {static_cast<uint8_t>(cdc1_progress),
                                         static_cast<uint8_t>(cdc2_progress)});

            // 更新MDC域进度
            simulateProgressWithDetails(DUCType::MDC,
                                        {"MDC_1", "MDC_2", "MDC_3"},
                                        {static_cast<uint8_t>(mdc1_progress),
                                         static_cast<uint8_t>(mdc2_progress),
                                         static_cast<uint8_t>(mdc3_progress)});

            // 更新ZCU域进度
            simulateProgressWithDetails(DUCType::ZCU,
                                        {"ZCU_1"},
                                        {static_cast<uint8_t>(zcu1_progress)});

            // 运行事件循环让定时器工作
            for (int j = 0; j < 10; ++j)
            {
                m_eventLoop->LoopOnce();
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }

            std::cout << "实际总体进度: " << static_cast<int>(m_lastProgress)
                      << "%" << std::endl;
            std::cout << std::string(80, '-') << std::endl;

            // 如果所有设备都达到100%，提前结束
            if (cdc1_progress >= 100 && cdc2_progress >= 100 &&
                mdc1_progress >= 100 && mdc2_progress >= 100 &&
                mdc3_progress >= 100 && zcu1_progress >= 100)
            {
                std::cout << "所有设备已达到100%，更新完成！" << std::endl;
                break;
            }
        }

        // m_progressFitting.Stop();
        std::cout << "\n完整OTA更新过程模拟结束" << std::endl;
        std::cout << "最终进度: " << static_cast<int>(m_lastProgress) << "%"
                  << std::endl;
        std::cout << "全部完成: " << (m_allFinished ? "是" : "否") << std::endl;
    }

private:
    ProgressFitting m_progressFitting;
    EventLoop *m_eventLoop = nullptr;
    uint8_t m_lastProgress = 0;
    bool m_allFinished = false;
};

int main()
{
    std::cout << "进度拟合模块权重计算测试程序" << std::endl;
    std::cout << "本程序将测试进度计算的准确性和权重分配" << std::endl;

    try
    {
        ProgressTestManager testManager;

        // 运行完整的OTA更新模拟
        testManager.runCompleteOTASimulation();

        std::cout << "\n测试程序执行完成！" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return -1;
    }

    return 0;
}