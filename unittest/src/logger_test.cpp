#include "logger/logger.h"
#include <iostream>
#include <thread>
#include <chrono>
#include "base/singleton.h"
#include <string>

using namespace seres::fotamaster;

void LogExample() {
    // 配置日志系统
#if 0
    LogConfig config;
    config.log_file_path = "logs/log_test.log";     // 日志文件路径
    config.max_file_size = 5 * 1024 * 1024;         // 5MB
    config.max_files = 3;                           // 最多保留3个文件
    config.console_output = true;                   // 输出到控制台
    config.log_level = LogLevel::kLogLevelDebug;    // 日志级别

    // 初始化日志系统
    base::Singleton<Logger>::Instance().Init(config);
#else
    std::string configRealpath = LOGGER_CFG_FILE_PATH_PERFIX + std::string("/logger_config.json");
    std::cout << "configRealpath: " << configRealpath << std::endl;
    // 初始化日志系统
    base::Singleton<Logger>::Instance().Init(std::move(configRealpath));
#endif

    // 使用日志宏记录不同级别的日志
    LOG_TRACE("这是一条跟踪日志");
    LOG_DEBUG("这是一条调试日志");
    LOG_INFO("这是一条信息日志");
    LOG_WARN("这是一条警告日志");
    LOG_ERROR("这是一条错误日志");
    LOG_CRITICAL("这是一条严重错误日志");

    // 使用格式化字符串
    int value = 42;
    LOG_INFO("当前值是: %d", value);

    // 多线程测试
    std::cout << "开始多线程日志测试..." << std::endl;

    auto thread_func = [](int thread_id) {
        for (int i = 0; i < 10; ++i) {
            LOG_INFO("线程 %d 计数: %d", thread_id, i);
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    };

    std::thread t1(thread_func, 1);
    std::thread t2(thread_func, 2);
    std::thread t3(thread_func, 3);

    t1.join();
    t2.join();
    t3.join();

    std::cout << "多线程日志测试完成" << std::endl;
}

int main() {
    try {
        LogExample();
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "异常: " << e.what() << std::endl;
        return 1;
    }
}