#include "rpc/OTA_DucInterface_gen_server.hpp"
#include <chrono>
#include <cstdlib>
#include <iostream>
#include <memory>
#include <string>
#include <thread>
#include <vector>

using namespace std;
using namespace seres::ota_duc_service;

class DucService_impl : public Interface_DucServiceInterface_BASE
{
private:
    dds::domain::DomainParticipant m_participant;
    dds::pub::Publisher publisher;

    // 主题
    dds::topic::Topic<OTA_DucDataUnion> m_topic;

    // 发布者
    dds::pub::DataWriter<OTA_DucDataUnion> m_writer;

public:
    DucService_impl(dds::domain::DomainParticipant participant)
        : m_participant(participant), publisher(m_participant),
          m_topic(m_participant, CDC_TOPIC_NAME), m_writer(publisher, m_topic)
    {
        std::cout << "=== [DUC Server] 初始化完成" << std::endl;
    }

    // 实现RPC接口
    virtual ReturnCode inventoryCollection(
        const SelectedInventoryList &inventory_list) override
    {
        std::cout << "--[DUC Server] -- inventoryCollection called"
                  << std::endl;
        // 创建示例数据
        std::thread([this] {
            InventoryResult inventory_Result_list;
            InventoryInfo info;
            info.partNumber("TEST001");
            info.softwareVersion("1.0.0");
            info.supplierCode("SUP001");
            info.ecuName("TestECU");
            info.serialNumber("SN001");
            info.hardwareVersion("HW001");
            info.ecuBatchNumber("BATCH001");
            info.bootloaderVersion("BL001");
            info.backupVersion("BK001");
            info.SeamlessModeSupport(true);

            inventory_Result_list.InventoryLists().push_back(info);
            OTA_DucDataUnion data;
            data.inventoryResult(inventory_Result_list);
            sleep(3);
            // 发布结果
            m_writer->write(data);
            std::cout << "pub inventory_Result_list success\n";
        }).detach();

        return ReturnCode::OK;
    }

    virtual ReturnCode stopInventoryCollection() override
    {
        std::cout << "--[DUC Server] -- stopInventoryCollection called"
                  << std::endl;
        return ReturnCode::OK;
    }

    virtual ReturnCode getInventoryResult(
        InventoryResult &inventory_list) override
    {
        std::cout << "--[DUC Server] -- getInventoryResult called" << std::endl;
        // 创建示例数据
        InventoryInfo info;
        info.partNumber("TEST001");
        info.softwareVersion("1.0.0");
        info.supplierCode("SUP001");
        info.ecuName("TestECU");
        info.serialNumber("SN001");
        info.hardwareVersion("HW001");
        info.ecuBatchNumber("BATCH001");
        info.bootloaderVersion("BL001");
        info.backupVersion("BK001");
        info.SeamlessModeSupport(true);

        inventory_list.InventoryLists().push_back(info);
        OTA_DucDataUnion data;
        data.inventoryResult(inventory_list);
        // 发布结果
        m_writer.write(data);
        return ReturnCode::OK;
    }

    virtual ReturnCode checkDownloadCondition(
        const DownloadConditionLists &conditions,
        DownloadConditionResult &condition_result) override
    {
        std::cout << "--[DUC Server] -- checkDownloadCondition called"
                  << std::endl;
        condition_result = DownloadConditionResult::NOEXCEPTION;
        return ReturnCode::OK;
    }

    virtual ReturnCode startDownload(
        const DownloadTaskLists &task_list) override
    {
        std::cout << "--[DUC Server] -- startDownload called" << std::endl;
        std::thread([this] {
            int p = 0;
            uint64_t download_count = 0;
            do
            {
                std::this_thread::sleep_for(std::chrono::seconds(1));
                p += 10;
                download_count += 10240;
                DownloadProgress download_progress;
                DownloadProgressInfo progress_info;
                progress_info.progressPercent(p);
                progress_info.packageName("test_package");
                progress_info.downloadedSize(download_count);
                progress_info.totalSize(102400);
                if (p == 100)
                    progress_info.status(DownloadStatus::DOWNLOAD_SUCCESS);
                else
                    progress_info.status(DownloadStatus::DOWNLOADING);

                download_progress.allFinished(false);
                download_progress.progressLists().push_back(progress_info);
                OTA_DucDataUnion data;
                data.downloadProgress(download_progress);
                // 发布进度
                m_writer.write(data);
                std::cout << "--[DUC Server] -- publish download progress: "
                          << p << "%" << std::endl;
            } while (p < 100);
        }).detach();

        return ReturnCode::OK;
    }

    virtual ReturnCode downloadCtrl(
        const DownloadCtrl &download_command) override
    {
        std::cout << "--[DUC Server] -- downloadCtrl called" << std::endl;
        return ReturnCode::OK;
    }

    virtual ReturnCode getDownloadProgress(
        DownloadProgress &download_progress) override
    {
        std::cout << "--[DUC Server] -- getDownloadProgress called"
                  << std::endl;
        // 创建示例进度数据
        DownloadProgressInfo progress_info;
        progress_info.progressPercent(50);
        progress_info.packageName("test_package");
        progress_info.downloadedSize(500);
        progress_info.totalSize(1000);
        progress_info.status(DownloadStatus::DOWNLOAD_SUCCESS);

        download_progress.allFinished(false);
        download_progress.progressLists().push_back(progress_info);
        return ReturnCode::OK;
    }

    virtual ReturnCode uzipPackages() override
    {
        std::cout << "--[DUC Server] -- uzipPackages called" << std::endl;
        std::thread([this] {
            UzipPackagesResult uzip_Result;
            uzip_Result.successed(true);
            uzip_Result.errorMsg("");
            std::this_thread::sleep_for(std::chrono::seconds(5));
            OTA_DucDataUnion data;
            data.uzipPackagesResult(uzip_Result);
            // 发布结果
            m_writer.write(data);
        }).detach();
        return ReturnCode::OK;
    }

    virtual ReturnCode getuzipPackagesResult(
        UzipPackagesResult &uzip_Result) override
    {
        std::cout << "--[DUC Server] -- getuzipPackagesResult called"
                  << std::endl;
        uzip_Result.successed(true);
        uzip_Result.errorMsg("");
        return ReturnCode::OK;
    }

    virtual ReturnCode startPackagesVerify() override
    {
        std::cout << "--[DUC Server] -- startPackagesVerify called"
                  << std::endl;
        std::thread([this] {
            PackagesVerifyResult verify_Result;
            verify_Result.successed(true);
            verify_Result.errorMsg("");
            // 发布结果
            OTA_DucDataUnion data;
            data.packagesVerifyResult(verify_Result);
            m_writer.write(data);
        }).detach();
        return ReturnCode::OK;
    }

    virtual ReturnCode getPackagesVerifyResult(
        PackagesVerifyResult &verify_Result) override
    {
        std::cout << "--[DUC Server] -- getPackagesVerifyResult called"
                  << std::endl;
        verify_Result.successed(true);
        verify_Result.errorMsg("");

        return ReturnCode::OK;
    }

    virtual ReturnCode checkUpdateCondition() override
    {
        std::cout << "--[DUC Server] -- checkUpdateCondition called"
                  << std::endl;
        std::thread([this] {
            CheckUpdateConditionResult checkcondition_Result;
            checkcondition_Result.passed(true);
            checkcondition_Result.errorCode(UpdateConditionErrorCode::NOERROR);
            std::this_thread::sleep_for(std::chrono::seconds(5));
            // 发布结果
            OTA_DucDataUnion data;
            data.checkUpdateConditionResult(checkcondition_Result);
            m_writer.write(data);
        }).detach();
        return ReturnCode::OK;
    }

    virtual ReturnCode getCheckUpdateConditionResult(
        CheckUpdateConditionResult &checkcondition_Result) override
    {
        std::cout << "--[DUC Server] -- getCheckUpdateConditionResult called"
                  << std::endl;
        checkcondition_Result.passed(true);
        checkcondition_Result.errorCode(UpdateConditionErrorCode::NOERROR);
        return ReturnCode::OK;
    }

    virtual ReturnCode startUpdate(const UpdateMode &mode,
                                   const UpdateDeviceList &update_list) override
    {
        std::cout << "--[DUC Server] -- startUpdate called" << std::endl;

        // 获取需要更新的设备列表
        std::thread([update_list, this] {
            int p = 0;
            do
            {
                std::this_thread::sleep_for(std::chrono::seconds(1));
                UpdateProgress update_progress;
                p += 10;
                for (const auto &device : update_list.updateDeviceLists())
                {
                    std::cout << "Device: " << device.serialNumber()
                              << std::endl;
                    DeviceUpdateProgress progress;
                    progress.progressPercent(min(100, p));
                    progress.deviceName(device.ecuName());
                    progress.deviceId(device.serialNumber());
                    progress.status(DeviceUpdateStatus::UPDATING);
                    progress.errorReason(UpdateErrorReason::ERROR_REASON_1);
                    update_progress.allFinished(p == 100);
                    update_progress.progressLists().push_back(progress);
                }
                // 发布进度
                OTA_DucDataUnion data;
                data.updateProgress(update_progress);
                std::cout << "--[DUC Server] -- publish update progress: " << p
                          << "%" << std::endl;
                m_writer.write(data);
            } while (p < 100);
        }).detach();

        return ReturnCode::OK;
    }

    virtual ReturnCode resumeUpdate() override
    {
        std::cout << "--[DUC Server] -- resumeUpdate called" << std::endl;
        return ReturnCode::OK;
    }

    virtual ReturnCode pauseUpdate() override
    {
        std::cout << "--[DUC Server] -- pauseUpdate called" << std::endl;
        return ReturnCode::OK;
    }

    virtual ReturnCode getUpdateProgress(
        UpdateProgress &update_progress) override
    {
        std::cout << "--[DUC Server] -- getUpdateProgress called" << std::endl;
        // 创建示例进度数据
        DeviceUpdateProgress progress;
        progress.progressPercent(75);
        progress.deviceName("TestDevice");
        progress.deviceId("DEV001");
        progress.status(DeviceUpdateStatus::UPDATING);
        progress.errorReason(UpdateErrorReason::ERROR_REASON_1);

        update_progress.allFinished(false);
        update_progress.progressLists().push_back(progress);

        // 发布进度
        OTA_DucDataUnion data;
        data.updateProgress(update_progress);
        m_writer.write(data);
        return ReturnCode::OK;
    }

    virtual ReturnCode activate() override
    {
        std::cout << "--[DUC Server] -- activate called" << std::endl;
        return ReturnCode::OK;
    }

    virtual ReturnCode rollback(
        const RollbackComponentList &component_list) override
    {
        std::cout << "--[DUC Server] -- rollback called" << std::endl;
        std::thread([this] {
            int p = 0;
            do
            {
                std::this_thread::sleep_for(std::chrono::seconds(1));
                p += 10;
                UpdateProgress update_progress;
                DeviceUpdateProgress progress;
                progress.progressPercent(p);
                progress.deviceName("RollbackDevice");
                progress.deviceId("DEV001");
                progress.status(DeviceUpdateStatus::UPDATING);
                progress.errorReason(UpdateErrorReason::ERROR_REASON_1);
                if (p == 100)
                    update_progress.allFinished(true);
                else
                    update_progress.allFinished(false);
                update_progress.progressLists().push_back(progress);
                // 发布进度
                OTA_DucDataUnion data;
                data.updateProgress(update_progress);
                std::cout << "--[DUC Server] -- publish rollback progress: "
                          << p << "%" << std::endl;
                m_writer.write(data);
            } while (p < 100);
        }).detach();
        return ReturnCode::OK;
    }

    virtual ReturnCode getRollbackProgress(
        UpdateProgress &update_progress) override
    {
        std::cout << "--[DUC Server] -- getRollbackProgress called"
                  << std::endl;
        // 创建示例进度数据
        DeviceUpdateProgress progress;
        progress.progressPercent(25);
        progress.deviceName("TestDevice");
        progress.deviceId("DEV001");
        progress.status(DeviceUpdateStatus::UPDATING);
        progress.errorReason(UpdateErrorReason::ERROR_REASON_1);

        update_progress.allFinished(false);
        update_progress.progressLists().push_back(progress);

        // 发布进度
        OTA_DucDataUnion data;
        data.updateProgress(update_progress);
        m_writer.write(data);
        return ReturnCode::OK;
    }

    virtual ReturnCode uploadLog() override
    {
        std::cout << "--[DUC Server] -- uploadLog called" << std::endl;
        return ReturnCode::OK;
    }
};

int main()
{
    try
    {
        std::cout << "=== [DUC Server] Starting..." << std::endl;

        // 创建RPC服务器
        dds::rpc::Server server(dds::rpc::ServerParam(4));

        // 配置服务参数
        dds::domain::DomainParticipant participant(1);
        dds::rpc::ServiceParams cdc_param(participant);
        cdc_param.serviceName(CDC_SERVICE_NAME);

        dds::rpc::ServiceParams mdc_param(participant);
        mdc_param.serviceName(MDC_SERVICE_NAME);

        dds::rpc::ServiceParams zcu_param(participant);
        zcu_param.serviceName(ZCU_SERVICE_NAME);
        // 创建服务实现
        std::shared_ptr<DucService_impl> impl =
            std::make_shared<DucService_impl>(participant);

        // 创建服务
        DucServiceInterfaceService cdc_service(impl, server, cdc_param);
        DucServiceInterfaceService mdc_service(impl, server, mdc_param);
        DucServiceInterfaceService zcu_service(impl, server, zcu_param);

        std::cout << "=== [DUC Server] Running..." << std::endl;

        // 运行服务器
        server.run();

        return 0;
    }
    catch (const dds::core::Exception &e)
    {
        std::cerr << "=== [DUC Server] Exception: " << e.what() << std::endl;
        return EXIT_FAILURE;
    }
}