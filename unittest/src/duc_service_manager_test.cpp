#include <chrono>
#include <condition_variable>
#include <cstdlib>
#include <functional>
#include <iostream>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <vector>

#include "dds_service_manager/duc_service_manager.h"
#include "ev_loop/eventloop_manager.h"
#include "logger/logger.h"
#include "progress_fitting/progress_fitting.h"

using namespace std;
using namespace seres::fotamaster;
using namespace seres::ota_duc_service;

void waitForEnterAndClearScreen()
{
    cout << "\nPress Enter to continue...";
    cin.ignore(numeric_limits<streamsize>::max(), '\n'); // Wait for Enter
    // Clear the screen (works on most terminals)
    cout << "\033[2J\033[1;1H"; // ANSI escape code for clear screen
}

void clearScreen()
{
    cout << "\033[2J\033[1;1H"; // ANSI escape code for clear screen
}

void displayTestMenu()
{
    clearScreen();
    cout << "\n=== DUC Service Manager Test Menu ===" << endl;
    cout << "Please select a test to run:" << endl;
    cout << "1. Inventory Collection Test" << endl;
    cout << "2. Download Test" << endl;
    cout << "3. Unzip Test" << endl;
    cout << "4. Package Verification Test" << endl;
    cout << "5. Update Condition Check Test" << endl;
    cout << "6. Update Test (Enhanced Multi-Domain)" << endl;
    cout << "7. Rollback Test" << endl;
    cout << "8. Activation Test" << endl;
    cout << "9. Log Upload Test" << endl;
    cout << "10. Run All Tests" << endl;
    cout << "0. Exit" << endl;
    cout << "Enter your choice (0-10): ";
}

int getUserChoice()
{
    int choice;
    while (!(cin >> choice) || choice < 0 || choice > 10)
    {
        cout << "Invalid input. Please enter a number between 0 and 10: ";
        cin.clear();
        cin.ignore(numeric_limits<streamsize>::max(), '\n');
    }
    cin.ignore(numeric_limits<streamsize>::max(),
               '\n'); // Clear the input buffer
    return choice;
}

// Topic callback functions
void onInventoryResult(const InventoryResult &result)
{
    LOG_INFO("Callback::::::::::[Received inventory result:");
    for (const auto &info : result.InventoryLists())
    {
        LOG_INFO("  ECU: %s, Version: %s",
                 info.ecuName().c_str(),
                 info.softwareVersion().c_str());
    }
}

void onDownloadProgress(const DownloadProgress &progress)
{
    LOG_INFO("Callback::::::::::[Received download progress:");
    for (const auto &info : progress.progressLists())
    {
        LOG_INFO("  Package name: %s, Progress: %d%%, Status: %d",
                 info.packageName().c_str(),
                 info.progressPercent(),
                 static_cast<int>(info.status()));
    }
}

void onUzipResult(const UzipPackagesResult &result)
{
    LOG_INFO("Callback::::::::::[Received unzip result: Success=%d, Error=%s",
             result.successed(),
             result.errorMsg().c_str());
}

void onVerifyResult(const PackagesVerifyResult &result)
{
    LOG_INFO(
        "Callback::::::::::[Received verification result: Success=%d, Error=%s",
        result.successed(),
        result.errorMsg().c_str());
}

void onCheckConditionResult(const CheckUpdateConditionResult &result)
{
    LOG_INFO("Callback::::::::::[Received check condition result: Passed=%d, "
             "Error code=%d",
             result.passed(),
             static_cast<int>(result.errorCode()));
}

void onUpdateProgress(const UpdateProgress &progress)
{
    LOG_INFO("Callback::::::::::[Received update progress:");
    for (const auto &info : progress.progressLists())
    {
        LOG_INFO("  Device: %s, Progress: %d%%, Status: %d",
                 info.deviceName().c_str(),
                 info.progressPercent(),
                 static_cast<int>(info.status()));
    }
}

void onRollbackProgress(const UpdateProgress &progress)
{
    LOG_INFO("Callback::::::::::[Received rollback progress:");
    for (const auto &info : progress.progressLists())
    {
        LOG_INFO("  Device: %s, Progress: %d%%, Status: %d",
                 info.deviceName().c_str(),
                 info.progressPercent(),
                 static_cast<int>(info.status()));
    }
}

// Service status callback
void onServiceStatus(DUCType type, bool connected)
{
    LOG_INFO("Callback::::::::::[Service status changed: Type=%s, Connected=%d",
             ducTypeToString(type).c_str(),
             connected);
}

// Test function declarations
bool testInventoryCollection(DUCServiceManager &manager);
bool testDownload(DUCServiceManager &manager);
bool testUnzip(DUCServiceManager &manager);
bool testPackageVerification(DUCServiceManager &manager);
bool testUpdateConditionCheck(DUCServiceManager &manager);
bool testUpdate(DUCServiceManager &manager);
bool testRollback(DUCServiceManager &manager);
bool testActivation(DUCServiceManager &manager);
bool testLogUpload(DUCServiceManager &manager);
bool runAllTests(DUCServiceManager &manager);
bool runSelectedTest(int choice, DUCServiceManager &manager);

// Helper function to create devices for different domains
std::vector<InventoryInfo> createDevicesForDomain(DUCType ducType)
{
    std::vector<InventoryInfo> devices;
    std::string domainName = ducTypeToString(ducType);

    // Different number of devices for each domain
    int deviceCount = 0;
    std::string devicePrefix = "";

    switch (ducType)
    {
    case DUCType::CDC:
        deviceCount = 3;
        devicePrefix = "CDC_ECU";
        break;
    case DUCType::MDC:
        deviceCount = 2;
        devicePrefix = "MDC_ECU";
        break;
    case DUCType::ZCU:
        deviceCount = 4;
        devicePrefix = "ZCU_ECU";
        break;
    default:
        deviceCount = 1;
        devicePrefix = "UNKNOWN_ECU";
        break;
    }

    for (int i = 0; i < deviceCount; i++)
    {
        InventoryInfo device;
        device.serialNumber(domainName + "_SN_" + std::to_string(i + 1));
        device.softwareVersion("1.0." + std::to_string(i));
        device.supplierCode("SUP_" + domainName);
        device.ecuName(devicePrefix + "_" + std::to_string(i + 1));
        devices.push_back(device);
    }

    return devices;
}

// Test function implementations
bool testInventoryCollection(DUCServiceManager &manager)
{
    waitForEnterAndClearScreen();
    LOG_INFO("\n=== Starting Inventory Collection Test ===");

    SelectedInventoryList inventoryList;
    inventoryList.inventoryLists().push_back("123123");

    if (!manager.subscribeInventoryResult(DUCType::CDC, onInventoryResult))
    {
        LOG_ERROR("Failed to subscribe to inventory result topic");
        return false;
    }

    ReturnCode ret = manager.inventoryCollection(DUCType::CDC, inventoryList);
    LOG_INFO("inventoryCollection returned: %s",
             returnCodeToString(ret).c_str());

    std::this_thread::sleep_for(std::chrono::seconds(4));

    return ret == ReturnCode::OK;
}

bool testDownload(DUCServiceManager &manager)
{
    waitForEnterAndClearScreen();
    LOG_INFO("\n=== Starting Download Test ===");

    DownloadTaskLists tasks;
    DownloadTaskInfo task;
    task.taskId("TASK001");
    task.packageVersion("1.0.0");
    task.packageName("test_package");
    task.packageUrl("http://test.com/package");
    task.packageSize("1024");
    task.packageMd5("md5sum");
    tasks.taskLists().push_back(task);

    if (!manager.subscribeDownloadProgress(DUCType::CDC, onDownloadProgress))
    {
        LOG_ERROR("Failed to subscribe to download progress topic");
        return false;
    }

    ReturnCode ret = manager.startDownload(DUCType::CDC, tasks);
    LOG_INFO("startDownload returned: %s", returnCodeToString(ret).c_str());

    std::this_thread::sleep_for(std::chrono::seconds(12));

    return ret == ReturnCode::OK;
}

bool testUnzip(DUCServiceManager &manager)
{
    waitForEnterAndClearScreen();
    LOG_INFO("\n=== Starting Unzip Test ===");

    if (!manager.subscribeUzipPackagesResult(DUCType::CDC, onUzipResult))
    {
        LOG_ERROR("Failed to subscribe to unzip result topic");
        return false;
    }

    ReturnCode ret = manager.uzipPackages(DUCType::CDC);
    LOG_INFO("uzipPackages returned: %s", returnCodeToString(ret).c_str());

    std::this_thread::sleep_for(std::chrono::seconds(5));

    UzipPackagesResult uzipResult;
    ret = manager.getuzipPackagesResult(DUCType::CDC, uzipResult);
    LOG_INFO("getuzipPackagesResult returned: %s",
             returnCodeToString(ret).c_str());

    return ret == ReturnCode::OK;
}

bool testPackageVerification(DUCServiceManager &manager)
{
    waitForEnterAndClearScreen();
    LOG_INFO("\n=== Starting Package Verification Test ===");

    if (!manager.subscribePackagesVerifyResult(DUCType::CDC, onVerifyResult))
    {
        LOG_ERROR("Failed to subscribe to verification result topic");
        return false;
    }

    ReturnCode ret = manager.startPackagesVerify(DUCType::CDC);
    LOG_INFO("startPackagesVerify returned: %s",
             returnCodeToString(ret).c_str());

    std::this_thread::sleep_for(std::chrono::seconds(5));

    PackagesVerifyResult verifyResult;
    ret = manager.getPackagesVerifyResult(DUCType::CDC, verifyResult);
    LOG_INFO("getPackagesVerifyResult successed? : %d",
             verifyResult.successed());

    return ret == ReturnCode::OK;
}

bool testUpdateConditionCheck(DUCServiceManager &manager)
{
    waitForEnterAndClearScreen();
    LOG_INFO("\n=== Starting Update Condition Check Test ===");

    if (!manager.subscribeCheckUpdateConditionResult(DUCType::CDC,
                                                     onCheckConditionResult))
    {
        LOG_ERROR("Failed to subscribe to check condition result topic");
        return false;
    }

    ReturnCode ret = manager.checkUpdateCondition(DUCType::CDC);
    LOG_INFO("checkUpdateCondition returned: %s",
             returnCodeToString(ret).c_str());

    std::this_thread::sleep_for(std::chrono::seconds(5));

    CheckUpdateConditionResult checkResult;
    ret = manager.getCheckUpdateConditionResult(DUCType::CDC, checkResult);
    LOG_INFO("getCheckUpdateConditionResult returned: %s",
             returnCodeToString(ret).c_str());

    return ret == ReturnCode::OK;
}

bool testUpdate(DUCServiceManager &manager)
{
    waitForEnterAndClearScreen();
    LOG_INFO("\n=== Starting Enhanced Update Test ===");

    // Initialize ProgressFitting
    ProgressFitting progressFit;
    progressFit.Initialize(
        [&](uint8_t progress, bool allFinished) {
            LOG_INFO("Overall Progress Callback: %d%%, All Finished: %s",
                     progress,
                     allFinished ? "Yes" : "No");
        },
        2);

    bool allDomainsSuccess = true;

    // Test each domain with different devices
    std::vector<DUCType> domains = {DUCType::CDC, DUCType::MDC, DUCType::ZCU};

    for (DUCType domain : domains)
    {
        LOG_INFO("\n--- Testing %s Domain ---",
                 ducTypeToString(domain).c_str());

        // Create devices for this domain
        std::vector<InventoryInfo> devices = createDevicesForDomain(domain);

        LOG_INFO("Created %zu devices for %s domain:",
                 devices.size(),
                 ducTypeToString(domain).c_str());
        for (const auto &device : devices)
        {
            LOG_INFO("  Device: %s (SN: %s, Version: %s)",
                     device.ecuName().c_str(),
                     device.serialNumber().c_str(),
                     device.softwareVersion().c_str());
        }

        // Subscribe to update progress for this domain
        if (!manager.subscribeUpdateProgress(domain, onUpdateProgress))
        {
            LOG_ERROR("Failed to subscribe to update progress topic for %s",
                      ducTypeToString(domain).c_str());
            allDomainsSuccess = false;
            continue;
        }

        // Add devices to progress fitting
        progressFit.AddUpdateDevice(domain, devices);

        // Create update device list
        UpdateDeviceList updateList;
        for (const auto &device : devices)
        {
            updateList.updateDeviceLists().push_back(device);
        }

        // Start update for this domain
        ReturnCode ret =
            manager.startUpdate(domain, UpdateMode::FormalMode, updateList);
        LOG_INFO("startUpdate for %s returned: %s",
                 ducTypeToString(domain).c_str(),
                 returnCodeToString(ret).c_str());

        if (ret != ReturnCode::OK)
        {
            allDomainsSuccess = false;
        }
    }

    // Get event loop for proper thread management
    auto &eventLoopManager = base::Singleton<EventLoopManager>::Instance();
    auto eventLoop = eventLoopManager.GetDefaultLoop();

    // Start progress fitting in event loop thread to avoid timer thread issues
    bool progressStarted = false;
    std::mutex progressMutex;
    std::condition_variable progressCv;

    eventLoop->RunInRunningLoopThread(
        [&progressFit, &progressStarted, &progressMutex, &progressCv]() {
            progressStarted = progressFit.Start();
            {
                std::lock_guard<std::mutex> lock(progressMutex);
            }
            progressCv.notify_one();
        });

    // Wait for progress fitting to start
    {
        std::unique_lock<std::mutex> lock(progressMutex);
        progressCv.wait_for(lock, std::chrono::seconds(2));
    }

    if (!progressStarted)
    {
        LOG_ERROR("Failed to start progress fitting");
        allDomainsSuccess = false;
    }
    else
    {
        LOG_INFO(
            "Update test initiated for all domains. Progress fitting started.");

        // Wait for test to complete
        std::this_thread::sleep_for(std::chrono::seconds(10));

        // Stop progress fitting in event loop thread
        eventLoop->RunInRunningLoopThread(
            [&progressFit]() { progressFit.Stop(); });
    }

    return allDomainsSuccess;
}

bool testRollback(DUCServiceManager &manager)
{
    waitForEnterAndClearScreen();
    LOG_INFO("\n=== Starting Rollback Test ===");

    RollbackComponentList rollbackList;
    // Note: rollbackList.rollbackLists().push_back(); - add components as needed

    ReturnCode ret = manager.rollback(DUCType::CDC, rollbackList);
    LOG_INFO("rollback returned: %s", returnCodeToString(ret).c_str());

    std::this_thread::sleep_for(std::chrono::seconds(11));

    return ret == ReturnCode::OK;
}

bool testActivation(DUCServiceManager &manager)
{
    waitForEnterAndClearScreen();
    LOG_INFO("\n=== Starting Activation Test ===");

    ReturnCode ret = manager.activate(DUCType::CDC);
    LOG_INFO("activate returned: %s", returnCodeToString(ret).c_str());

    std::this_thread::sleep_for(std::chrono::seconds(2));

    return ret == ReturnCode::OK;
}

bool testLogUpload(DUCServiceManager &manager)
{
    waitForEnterAndClearScreen();
    LOG_INFO("\n=== Starting Log Upload Test ===");

    ReturnCode ret = manager.uploadLog(DUCType::CDC);
    LOG_INFO("uploadLog returned: %s", returnCodeToString(ret).c_str());

    std::this_thread::sleep_for(std::chrono::seconds(2));

    return ret == ReturnCode::OK;
}

bool runAllTests(DUCServiceManager &manager)
{
    clearScreen();
    LOG_INFO("\n=== Running All Tests ===");

    bool allTestsPassed = true;

    LOG_INFO("Starting test sequence...");

    allTestsPassed &= testInventoryCollection(manager);
    allTestsPassed &= testDownload(manager);
    allTestsPassed &= testUnzip(manager);
    allTestsPassed &= testPackageVerification(manager);
    allTestsPassed &= testUpdateConditionCheck(manager);
    allTestsPassed &= testUpdate(manager);
    allTestsPassed &= testRollback(manager);
    allTestsPassed &= testActivation(manager);
    allTestsPassed &= testLogUpload(manager);

    if (allTestsPassed)
    {
        LOG_INFO("=== All tests PASSED ===");
    }
    else
    {
        LOG_ERROR("=== Some tests FAILED ===");
    }

    return allTestsPassed;
}

bool runSelectedTest(int choice, DUCServiceManager &manager)
{
    switch (choice)
    {
    case 1:
        return testInventoryCollection(manager);
    case 2:
        return testDownload(manager);
    case 3:
        return testUnzip(manager);
    case 4:
        return testPackageVerification(manager);
    case 5:
        return testUpdateConditionCheck(manager);
    case 6:
        return testUpdate(manager);
    case 7:
        return testRollback(manager);
    case 8:
        return testActivation(manager);
    case 9:
        return testLogUpload(manager);
    case 10:
        return runAllTests(manager);
    default:
        LOG_ERROR("Invalid test choice: %d", choice);
        return false;
    }
}


int main()
{
    LogConfig config;
    config.log_file_path = "logs/ota_master.log";
    config.max_file_size = 5 * 1024 * 1024;
    config.max_files = 3;
    config.console_output = true;
    config.log_level = LogLevel::kLogLevelDebug;

    base::Singleton<Logger>::Instance().Init(config);
    try
    {
        LOG_INFO("=== [DUC Service Manager Test] Starting...");

        auto &manager = base::Singleton<DUCServiceManager>::Instance();
        if (!manager.initialize(1))
        {
            LOG_ERROR("Failed to initialize service manager");
            return EXIT_FAILURE;
        }
        LOG_INFO("Service manager initialized successfully");

        if (!manager.createClient(DUCType::CDC))
        {
            LOG_ERROR("Failed to create CDC domain client");
            return EXIT_FAILURE;
        }
        LOG_INFO("CDC domain client created successfully");
        if (!manager.createClient(DUCType::MDC))
        {
            LOG_ERROR("Failed to create MDC domain client");
            return EXIT_FAILURE;
        }
        LOG_INFO("MDC domain client created successfully");
        if (!manager.createClient(DUCType::ZCU))
        {
            LOG_ERROR("Failed to create ZCU domain client");
            return EXIT_FAILURE;
        }
        LOG_INFO("ZCU domain client created successfully");

        LOG_INFO("Waiting for service connection...");
        std::thread([&] {
            base::Singleton<EventLoopManager>::Instance()
                .GetDefaultLoop()
                ->LoopForever();
        }).detach();

        // Give event loop time to start
        std::this_thread::sleep_for(std::chrono::milliseconds(500));

        // Interactive test menu
        int choice;
        bool testResult = true;

        do
        {
            displayTestMenu();
            choice = getUserChoice();

            if (choice == 0)
            {
                LOG_INFO("Exiting test program...");
                break;
            }

            testResult = runSelectedTest(choice, manager);

            if (testResult)
            {
                LOG_INFO("Test completed successfully!");
            }
            else
            {
                LOG_ERROR("Test failed!");
            }

            if (choice != 10) // Don't wait after "Run All Tests"
            {
                cout << "\nPress Enter to return to menu...";
                cin.get();
            }

        } while (choice != 0);

        // Terminate event loop
        auto &eventLoopManager = base::Singleton<EventLoopManager>::Instance();
        auto eventLoop = eventLoopManager.GetDefaultLoop();
        eventLoop->TerminateLoop();

        manager.shutdown();
        LOG_INFO("=== [DUC Service Manager Test] Program terminated ===");
        return EXIT_SUCCESS;
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("Test exception: %s", e.what());
        return EXIT_FAILURE;
    }
}