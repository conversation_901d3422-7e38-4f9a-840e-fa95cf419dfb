#include <chrono>
#include <cstdlib>
#include <functional>
#include <iostream>
#include <memory>
#include <string>
#include <thread>

#include "dds_service_manager/duc_service_manager.h"
#include "ev_loop/eventloop_manager.h"
#include "logger/logger.h"
#include "progress_fitting/progress_fitting.h"

using namespace std;
using namespace seres::fotamaster;
using namespace seres::ota_duc_service;

void waitForEnterAndClearScreen()
{
    cout << "\nPress Enter to continue...";
    cin.ignore(numeric_limits<streamsize>::max(), '\n'); // Wait for Enter
    // Clear the screen (works on most terminals)
    cout << "\033[2J\033[1;1H"; // ANSI escape code for clear screen
}

// Topic callback functions
void onInventoryResult(const InventoryResult &result)
{
    LOG_INFO("Callback::::::::::[Received inventory result:");
    for (const auto &info : result.InventoryLists())
    {
        LOG_INFO("  ECU: %s, Version: %s",
                 info.ecuName().c_str(),
                 info.softwareVersion().c_str());
    }
}

void onDownloadProgress(const DownloadProgress &progress)
{
    LOG_INFO("Callback::::::::::[Received download progress:");
    for (const auto &info : progress.progressLists())
    {
        LOG_INFO("  Package name: %s, Progress: %d%%, Status: %d",
                 info.packageName().c_str(),
                 info.progressPercent(),
                 static_cast<int>(info.status()));
    }
}

void onUzipResult(const UzipPackagesResult &result)
{
    LOG_INFO("Callback::::::::::[Received unzip result: Success=%d, Error=%s",
             result.successed(),
             result.errorMsg().c_str());
}

void onVerifyResult(const PackagesVerifyResult &result)
{
    LOG_INFO(
        "Callback::::::::::[Received verification result: Success=%d, Error=%s",
        result.successed(),
        result.errorMsg().c_str());
}

void onCheckConditionResult(const CheckUpdateConditionResult &result)
{
    LOG_INFO("Callback::::::::::[Received check condition result: Passed=%d, "
             "Error code=%d",
             result.passed(),
             static_cast<int>(result.errorCode()));
}

void onUpdateProgress(const UpdateProgress &progress)
{
    LOG_INFO("Callback::::::::::[Received update progress:");
    for (const auto &info : progress.progressLists())
    {
        LOG_INFO("  Device: %s, Progress: %d%%, Status: %d",
                 info.deviceName().c_str(),
                 info.progressPercent(),
                 static_cast<int>(info.status()));
    }
}

void onRollbackProgress(const UpdateProgress &progress)
{
    LOG_INFO("Callback::::::::::[Received rollback progress:");
    for (const auto &info : progress.progressLists())
    {
        LOG_INFO("  Device: %s, Progress: %d%%, Status: %d",
                 info.deviceName().c_str(),
                 info.progressPercent(),
                 static_cast<int>(info.status()));
    }
}

// Service status callback
void onServiceStatus(DUCType type, bool connected)
{
    LOG_INFO("Callback::::::::::[Service status changed: Type=%s, Connected=%d",
             ducTypeToString(type).c_str(),
             connected);
}

int main()
{
    LogConfig config;
    config.log_file_path = "logs/ota_master.log";
    config.max_file_size = 5 * 1024 * 1024;
    config.max_files = 3;
    config.console_output = true;
    config.log_level = LogLevel::kLogLevelDebug;

    base::Singleton<Logger>::Instance().Init(config);
    try
    {
        LOG_INFO("=== [DUC Service Manager Test] Starting...");

        auto &manager = base::Singleton<DUCServiceManager>::Instance();
        if (!manager.initialize(1))
        {
            LOG_ERROR("Failed to initialize service manager");
            return EXIT_FAILURE;
        }
        LOG_INFO("Service manager initialized successfully");

        if (!manager.createClient(DUCType::CDC))
        {
            LOG_ERROR("Failed to create CDC domain client");
            return EXIT_FAILURE;
        }
        LOG_INFO("CDC domain client created successfully");
        if (!manager.createClient(DUCType::MDC))
        {
            LOG_ERROR("Failed to create CDC domain client");
            return EXIT_FAILURE;
        }
        LOG_INFO("MDC domain client created successfully");
        if (!manager.createClient(DUCType::ZCU))
        {
            LOG_ERROR("Failed to create CDC domain client");
            return EXIT_FAILURE;
        }
        LOG_INFO("ZCU domain client created successfully");


        LOG_INFO("Waiting for service connection...");

        auto &eventLoopManager = base::Singleton<EventLoopManager>::Instance();
        auto eventLoop = eventLoopManager.GetDefaultLoop();
        // Test inventory collection
        waitForEnterAndClearScreen();
        LOG_INFO("\n=== Starting Inventory Collection Test ===");
        SelectedInventoryList inventoryList;
        inventoryList.inventoryLists().push_back("123123");
        if (!manager.subscribeInventoryResult(DUCType::CDC, onInventoryResult))
        {
            LOG_ERROR("Failed to subscribe to inventory result topic");
        }
        ReturnCode ret =
            manager.inventoryCollection(DUCType::CDC, inventoryList);
        LOG_INFO("inventoryCollection returned: %s",
                 returnCodeToString(ret).c_str());
        std::this_thread::sleep_for(std::chrono::seconds(4));

        // Test download functionality
        waitForEnterAndClearScreen();
        LOG_INFO("\n=== Starting Download Test ===");
        DownloadTaskLists tasks;
        DownloadTaskInfo task;
        task.taskId("TASK001");
        task.packageVersion("1.0.0");
        task.packageName("test_package");
        task.packageUrl("http://test.com/package");
        task.packageSize("1024");
        task.packageMd5("md5sum");
        tasks.taskLists().push_back(task);
        if (!manager.subscribeDownloadProgress(DUCType::CDC,
                                               onDownloadProgress))
        {
            LOG_ERROR("Failed to subscribe to download progress topic");
        }
        ret = manager.startDownload(DUCType::CDC, tasks);
        std::this_thread::sleep_for(std::chrono::seconds(12));

        // Test unzip functionality
        waitForEnterAndClearScreen();
        LOG_INFO("\n=== Starting Unzip Test ===");
        if (!manager.subscribeUzipPackagesResult(DUCType::CDC, onUzipResult))
        {
            LOG_ERROR("Failed to subscribe to unzip result topic");
        }
        ret = manager.uzipPackages(DUCType::CDC);
        std::this_thread::sleep_for(std::chrono::seconds(5));
        UzipPackagesResult uzipResult;
        ret = manager.getuzipPackagesResult(DUCType::CDC, uzipResult);
        LOG_INFO("getuzipPackagesResult returned: %s",
                 returnCodeToString(ret).c_str());

        // Test package verification functionality
        waitForEnterAndClearScreen();
        LOG_INFO("\n=== Starting Package Verification Test ===");
        if (!manager.subscribePackagesVerifyResult(DUCType::CDC,
                                                   onVerifyResult))
        {
            LOG_ERROR("Failed to subscribe to verification result topic");
        }
        ret = manager.startPackagesVerify(DUCType::CDC);
        std::this_thread::sleep_for(std::chrono::seconds(5));
        PackagesVerifyResult verifyResult;
        ret = manager.getPackagesVerifyResult(DUCType::CDC, verifyResult);
        LOG_INFO("getPackagesVerifyResult successed? : %d",
                 verifyResult.successed());

        // Test update condition check
        waitForEnterAndClearScreen();
        LOG_INFO("\n=== Starting Update Condition Check Test ===");
        ret = manager.checkUpdateCondition(DUCType::CDC);
        std::this_thread::sleep_for(std::chrono::seconds(5));
        CheckUpdateConditionResult checkResult;
        if (!manager.subscribeCheckUpdateConditionResult(
                DUCType::CDC,
                onCheckConditionResult))
        {
            LOG_ERROR("Failed to subscribe to check condition result topic");
        }
        ret = manager.getCheckUpdateConditionResult(DUCType::CDC, checkResult);
        LOG_INFO("getCheckUpdateConditionResult returned:%s",
                 returnCodeToString(ret).c_str());

        // Test update functionality
        waitForEnterAndClearScreen();
        LOG_INFO("\n=== Starting Update Test ===");
        UpdateDeviceList updateList;
        for (int i = 0; i < 4; i++)
        {
            InventoryInfo updateInfo;
            updateInfo.serialNumber(std::to_string(i * 10));
            updateInfo.softwareVersion("1.0.0");
            updateInfo.supplierCode("SUP001");
            updateInfo.ecuName("TestECU" + std::to_string(i * 10));
            updateList.updateDeviceLists().push_back(updateInfo);
        }

        if (!manager.subscribeUpdateProgress(DUCType::CDC, onUpdateProgress))
        {
            LOG_ERROR("Failed to subscribe to update progress topic");
        }
        ProgressFitting progressFit;
        progressFit.Initialize(
            [&](uint8_t progress, bool allFinished) {
                LOG_INFO("OverAll Progress callBack: %d%%, All Finished: %s",
                         progress,
                         allFinished ? "Yes" : "No");
            },
            2);
        progressFit.AddUpdateDevice(DUCType::CDC,
                                    updateList.updateDeviceLists());

        ret = manager.startUpdate(DUCType::CDC,
                                  UpdateMode::FormalMode,
                                  updateList);
        progressFit.Start();
        eventLoop->LoopForever();

        std::this_thread::sleep_for(std::chrono::seconds(12));

        // Test rollback functionality
        waitForEnterAndClearScreen();
        LOG_INFO("\n=== Starting Rollback Test ===");
        RollbackComponentList rollbackList;
        // rollbackList.rollbackLists().push_back();
        ret = manager.rollback(DUCType::CDC, rollbackList);
        std::this_thread::sleep_for(std::chrono::seconds(11));

        // Test activation functionality
        waitForEnterAndClearScreen();
        LOG_INFO("\n=== Starting Activation Test ===");
        ret = manager.activate(DUCType::CDC);

        // Test log upload functionality
        waitForEnterAndClearScreen();
        LOG_INFO("\n=== Starting Log Upload Test ===");
        ret = manager.uploadLog(DUCType::CDC);

        // Wait for callbacks
        std::this_thread::sleep_for(std::chrono::seconds(2));

        manager.shutdown();
        LOG_INFO("=== [DUC Service Manager Test] Done.");
        return EXIT_SUCCESS;
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("Test exception: %s", e.what());
        return EXIT_FAILURE;
    }
}