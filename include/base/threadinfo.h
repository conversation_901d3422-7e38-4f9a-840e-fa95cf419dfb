#pragma once

#include "base/macros.h"
#include <cstdint>
#include <string>

namespace seres
{
namespace fotamaster
{
namespace base
{

namespace curthread
{

extern __thread int t_cachedTid;
void CacheTid();

inline int tid()
{
    // if (__builtin_expect(t_cachedTid == 0, 0))
    if (UNLIKELY(t_cachedTid == 0))
    {
        CacheTid();
    }
    return t_cachedTid;
}

bool is_main_thread();

bool set_thread_name(pthread_t thread_handle, const std::string &name);

std::string get_thread_name(pthread_t thread_handle);

} // namespace curthread

} // namespace base
} // namespace fotamaster
} // namespace seres