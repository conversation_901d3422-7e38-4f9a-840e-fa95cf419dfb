#pragma once

#include <cstdint>
#include <string>

namespace seres
{
namespace fotamaster
{

enum class ErrCode : int32_t
{
    kUnknowError = -1,
    // ...
    kInvalidArgs = 65536,
    /// A signal or timeout or event has been registered
    kRegisteredEvent,
    /// A callback is not registered
    kInvalidCallback,
};

inline std::string ErrToString(ErrCode errc)
{
    switch (errc)
    {
        case ErrCode::kUnknowError:
            return "kUnknowError";
        case ErrCode::kInvalidArgs:
            return "kInvalidArgs";
        case ErrCode::kRegisteredEvent:
            return "kRegisteredEvent";
        case ErrCode::kInvalidCallback:
            return "kInvalidCallback";
        default:
            return "undefined err code";
    }
}

}
} // namespace seres