#pragma once

#include "base/noncopyable.h"
#include "ev_loop/eventloop.h"
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>

namespace seres
{
namespace fotamaster
{

class EventLoopManager : base::Noncopyable
{
public:
    EventLoopManager();
    ~EventLoopManager();

    /// @brief  Get the default EventLoop that should be running in main thread of a process.
    ///
    /// @return EventLoop*
    ///
    EventLoop *GetDefaultLoop();

    /// @brief  Get a thread's EventLoop.
    ///
    /// If thread_id is std::thread::id(), the EventLoop of current thread will be returned.
    ///
    /// @param  thread_id
    /// @return EventLoop*
    ///
    EventLoop *GetThreadLoop(std::thread::id thread_id = std::thread::id());

private:
    std::string default_loop_name_;
    std::mutex loops_mutex_;
    std::unordered_map<std::string, std::unique_ptr<EventLoop>> loops_;
};

} // namespace fotamaster
} // namespace seres
