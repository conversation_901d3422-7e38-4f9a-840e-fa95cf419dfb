#pragma once

#include "ev_loop/error_code.h"
#include "ev_loop/signal_handler.h"
#include <functional>
#include <optional>
#include <unordered_map>

namespace seres
{
namespace fotamaster
{

class EventLoop;

/// @brief  a global signal manager run in DEFAULT EventLoop.
///
/// @note   Do NOT use this in other EventLoops
///
class SignalManager : private SignalHandler
{
public:
    using SignalCallback = std::function<void(int /*signum*/)>;

    /// @brief	Construct a new SignalManager object
    ///
    /// @param	loop
    ///
    explicit SignalManager(EventLoop *loop);
    virtual ~SignalManager();

    /// @brief	Set the signal handler
    ///
    /// @param	signum
    /// @param	callback
    /// @return	std::optional<ErrCode> 失败时有错误码值，成功没有值
    ///
    std::optional<ErrCode> RegisterHandler(int signum,
                                           SignalCallback &&callback);

    /// @brief
    ///
    /// @param	signum
    ///
    void UnregisterHandler(int signum);

    using SignalHandler::loop;

private:
    void HandleSignal(int signum) noexcept override;

private:
    std::unordered_map<int, SignalCallback> signal_handlers_;
};

} // namespace fotamaster
} // namespace seres
