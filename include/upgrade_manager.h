#pragma once

#include "base/noncopyable.h"
#include "OTA_DucData.hpp"
#include <cstdint>
#include <functional>

namespace seres
{
namespace fotamaster
{

enum class UpgradeMode : uint8_t
{
    kSeamlessUpgrade,
    kFormalUpgrade
};

class DUCServiceManager;

class UpgradeManager : base::Noncopyable
{
public:
    using ProgressCallback = std::function<void (/*TODO*/)>;
    using FailReasonCallback = std::function<void (/*TODO*/)>;

    UpgradeManager() = default;
    explicit UpgradeManager(DUCServiceManager *duc_service_manager);

    bool CheckUpgradeCondition();
    void ProgressFitting();

    bool StartUpgrade(const UpgradeMode &upgrade_mode);

    void RegisterProgressCallback(ProgressCallback &&callback)
    {
        progress_callback_ = std::move(callback);
    }

    void RegisterFailReasonCallback(FailReasonCallback &&callback)
    {
        fail_reason_callback_ = std::move(callback);
    }

private:
    DUCServiceManager *duc_srv_manager_{nullptr};
    ProgressCallback progress_callback_{nullptr};
    FailReasonCallback fail_reason_callback_{nullptr};
};

}
}