#pragma once


#include "state_machine/state.h"
#include "state_machine/ota_state_machine.h"

namespace seres
{
namespace fotamaster
{

class FormalUpgradeState : public State
{
public:
    FormalUpgradeState() = default;
    ~FormalUpgradeState(){};

    bool Enter() override;
    void Process(const EventData &data) override;
    void Exit() override;
private:
    void HandleRequestEvent(const OtaInState& ota_in_state);
};

} // namespace fotamaster
} // namespace seres