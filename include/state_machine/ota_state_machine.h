#pragma once

#include "state_machine/state.h"
#include <cstdint>
#include <memory>
#include <string>
#include <functional>
#include <any>
#include <variant>

namespace seres
{
namespace fotamaster
{

enum class OtaInState : uint8_t
{
    kOtaInIdleState,
    kOtaInDownloadState,
    kOtaInSeamlessUpgradeState,
    kOtaInSeamlessUpgradeFinishState,
    kOtaInPreFormalUpgradeState,
    kOtaInFormalUpgradeState,
    kOtaInFaultState,
    kOtaInRollbackState,
    kOtaInExitState,
    kOtaInNR = 0xFF
};

struct OtaTaskInfo
{
    std::string upgrade_info;
    std::string package_info;
};

struct OtaStateMsg
{
    OtaInState in_state;
    std::variant<OtaTaskInfo> state_param;
};

enum class StateEvent : uint8_t
{
    kStartDownload,
    kStartSeamlessUpgrade,
    kCheckPreCondition,
    kStartFormalUpgrade,
    kStartRollback,
    kFaultHandling,
};

class OtaStateMachine : public StateMachine
{
public:
    using EventMsgCallback = std::function<bool(const StateEvent &, const std::any &)>;
    explicit OtaStateMachine(EventLoop *event_loop);
    virtual ~OtaStateMachine();

    void InitState();

    void ChangeState(const std::string &new_state) override;

    void HandleEvent(const OtaStateMsg &ota_info);

    void RegisterEventHandle(EventMsgCallback &&callback)
    {
        event_cb_ = std::move(callback);
    }

    bool EventCallback(const StateEvent &state, const std::any &data = std::any());

    OtaStateMsg GetStateMsg() const
    {
        return ota_state_msg_;
    }

private:
    EventMsgCallback event_cb_{nullptr};
    OtaStateMsg ota_state_msg_;
};

} // namespace fotamaster
} // namespace seres