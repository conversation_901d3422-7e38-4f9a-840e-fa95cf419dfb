#pragma once


#include "state_machine/state.h"
#include "state_machine/ota_state_machine.h"

namespace seres
{
namespace fotamaster
{

class PreFormalUpgradeState : public State
{
public:
    PreFormalUpgradeState() = default;
    ~PreFormalUpgradeState(){};

    bool Enter() override;
    void Process(const EventData &data) override;
    void Exit() override;
private:
    void HandleRequestEvent(const OtaInState& ota_in_state);
};

} // namespace fotamaster
} // namespace seres