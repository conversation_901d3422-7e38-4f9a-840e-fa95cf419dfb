#pragma once

#include "base/noncopyable.h"
#include "dds_service_manager/duc_service_manager.h"
#include "OTA_VucServiceData.hpp"
#include <functional>

namespace seres
{
namespace fotamaster
{

class DUCServiceManager;

class DownloadManager : base::Noncopyable
{
public:
    using TotalDownloadProgress = seres::ota_vuc_service::TotalDownloadProgress;
    using ProgressCallback = std::function<void (const TotalDownloadProgress &)>;
    using DownloadFailReason = seres::ota_vuc_service::DownloadFailReason;
    using FailReasonCallback = std::function<void (const DownloadFailReason &)>;

    DownloadManager() = default;
    explicit DownloadManager(DUCServiceManager *duc_service_manager);

    void RegisterProgressCallback(ProgressCallback &&callback)
    {
        progress_callback_ = std::move(callback);
    }

    void RegisterFailReasonCallback(FailReasonCallback &&callback)
    {
        fail_reason_callback_ = std::move(callback);
    }

    bool CheckDownloadCondition();
    bool ProgressFitting(const DownloadProgress &progress);

    bool StartDownload();
    bool StopDownload();
    bool ResumeDownload();
    bool CancelDownload();

private:
    DUCServiceManager *duc_srv_manager_{nullptr};
    DownloadProgress download_progress_;
    ProgressCallback progress_callback_{nullptr};
    FailReasonCallback fail_reason_callback_{nullptr};
};

}
}
