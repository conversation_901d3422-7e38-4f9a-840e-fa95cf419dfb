#pragma once

#include "OTA_VucServiceData.hpp"
#include "base/noncopyable.h"
#include "dds_service_manager/duc_service_manager.h"
#include "device_config.h"
#include <atomic>
#include <functional>
#include <mutex>
#include <optional>
#include <string>
#include <vector>

namespace seres
{
namespace fotamaster
{
// class DUCServiceManager;

class InventoryManager : base::Noncopyable
{
public:
    using InventoryInfoList = seres::ota_vuc_service::InventoryInfoList;
    using InventoryCallback = std::function<void(const InventoryInfoList &)>;
    explicit InventoryManager(DUCServiceManager *duc_service_manager);

    void RegisterInventoryCallback(InventoryCallback &&callback)
    {
        inventory_callback_ = std::move(callback);
    }

    bool TriggerInventoryCollection();
    bool StopInventoryCollection();

private:
    bool Initialize();
    bool StartInventoryCollection(DUCType type,
                                  const SelectedInventoryList &inventory_list);

    bool CheckInventory(DUCType type, const InventoryResult &result);
    std::optional<std::vector<std::string>> CheckInventoryResult(
        const std::vector<std::string> &dev_list,
        const InventoryResult &result);
    void SaveInventoryResult(const InventoryResult &result);

private:
    DUCServiceManager *duc_srv_manager_{nullptr};
    InventoryCallback inventory_callback_{nullptr};
    ns::DeviceListConfig all_dev_list_;
    std::mutex inventory_info_list_mutex_;
    InventoryInfoList inventory_info_list_;
    std::atomic_uint8_t cdc_try_count_{2};
};

} // namespace fotamaster
} // namespace seres