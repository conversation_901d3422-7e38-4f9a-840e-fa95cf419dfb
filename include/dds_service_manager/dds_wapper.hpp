// GenericDdsWrapper.hpp
#pragma once
#include "base/noncopyable.h"
#include "dds/dds.hpp"
#include <functional>
#include <memory>
#include <mutex>
#include <optional>
#include <stdexcept>
#include <type_traits>
#include <unordered_map>

namespace seres
{
namespace fotamaster
{
namespace dds_wrapper
{

// TODO 增加QoS config

// ==================== 类型特征模板 ====================
template <typename DataUnion, typename TopicEnum>
struct DdsTypeTraits
{
    // 必须实现的静态方法
    template <typename T>
    static constexpr TopicEnum type_to_enum();

    template <typename T>
    static const auto &get_value(const DataUnion &data);

    template <typename T>
    static void set_value(DataUnion &data, const T &value);
};

// ==================== 通用结果类型 ====================
template <typename T>
struct DdsResult
{
    static_assert(!std::is_same_v<T, void>,
                  "DdsResult<T> cannot use T = void. Use DdsResult<void> "
                  "specialization.");

    std::optional<T> value;
    std::string error_msg;

    static DdsResult Success(T val)
    {
        DdsResult res;
        res.value = std::move(val);
        return res;
    }

    static DdsResult Failure(std::string &&msg)
    {
        DdsResult res;
        res.error_msg = std::move(msg);
        return res;
    }

    explicit operator bool() const
    {
        return value.has_value();
    }
    T &GetValue()
    {
        return *value;
    }
    const T &GetValue() const
    {
        return *value;
    }
};

template <>
struct DdsResult<void>
{
    bool has_value{false};
    std::string error_msg;

    static DdsResult Success()
    {
        DdsResult res;
        res.has_value = true;
        return res;
    }

    static DdsResult Failure(std::string &&msg)
    {
        DdsResult res;
        res.has_value = false;
        res.error_msg = std::move(msg);
        return res;
    }

    explicit operator bool() const
    {
        return has_value;
    }
};

// ==================== 通用Subscriber ====================
template <typename DataUnion,
          typename TopicEnum,
          typename Traits = DdsTypeTraits<DataUnion, TopicEnum>>
class UnionSubscriber : base::Noncopyable
{
public:
    template <typename T>
    using DataCallback = std::function<void(const T &)>;
    using CallbackMap =
        std::unordered_map<TopicEnum,
                            std::function<void(const DataUnion &)>>;
    static DdsResult<std::shared_ptr<UnionSubscriber>> Create(
        uint32_t domain_id,
        const std::string &topic_name)
    {
        try
        {
            dds::domain::DomainParticipant participant(domain_id);
            dds::sub::Subscriber subscriber(participant);
            dds::topic::Topic<DataUnion> topic(participant, topic_name);

            auto *instance = new UnionSubscriber(std::move(participant),
                                                   std::move(subscriber),
                                                   std::move(topic));
            return DdsResult<std::shared_ptr<UnionSubscriber>>::Success(
                std::shared_ptr<UnionSubscriber>(instance));
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<std::shared_ptr<UnionSubscriber>>::Failure(
                "DDS error: " + std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<std::shared_ptr<UnionSubscriber>>::Failure(
                "System error: " + std::string(e.what()));
        }
    }

    template <typename T>
    DdsResult<bool> Subscribe(DataCallback<T> callback)
    {
        static_assert(IsSupportedType<T>(), "Unsupported message type");

        const auto topic_enum = Traits::template type_to_enum<T>();
        std::lock_guard<std::mutex> lock(mutex_);

        if (callbacks_.count(topic_enum))
        {
            return DdsResult<bool>::Failure("Type already subscribed");
        }

        callbacks_.emplace(topic_enum,
                           [cb = std::move(callback)](const DataUnion &data) {
                               cb(Traits::template get_value<T>(data));
                           });

        return DdsResult<bool>::Success(true);
    }

    DdsResult<bool> IsConnected()
    {
        try
        {
            auto status = reader_.subscription_matched_status();
            return DdsResult<bool>::Success(status.current_count() > 0);
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<bool>::Failure("DDS error: " +
                                            std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<bool>::Failure("System error: " +
                                            std::string(e.what()));
        }
    }

    DdsResult<int> GetMatchedPublishersCount()
    {
        try
        {
            auto status = reader_.subscription_matched_status();
            return DdsResult<int>::Success(status.current_count());
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<int>::Failure("DDS error: " +
                                           std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<int>::Failure("System error: " +
                                           std::string(e.what()));
        }
    }

private:
    UnionSubscriber(dds::domain::DomainParticipant &&participant,
                      dds::sub::Subscriber &&subscriber,
                      dds::topic::Topic<DataUnion> &&topic)
        : participant_(std::move(participant)),
          subscriber_(std::move(subscriber)), topic_(std::move(topic)),
          reader_(subscriber_, topic_),
          listener_(std::make_unique<Listener>(callbacks_, mutex_))
    {
        reader_.listener(listener_.get(),
                         dds::core::status::StatusMask::data_available());
    }

    template <typename T>
    static constexpr bool IsSupportedType()
    {
        // return std::is_same_v<T,
        //                       decltype(Traits::template get_value<T>(
        //                           std::declval<DataUnion>()))>;
        using ReturnType = decltype(Traits::template get_value<T>(std::declval<DataUnion>()));
        // 移除引用和cv限定符进行比较
        return std::is_same_v<std::decay_t<ReturnType>, std::decay_t<T>>;
    }

    class Listener : public dds::sub::NoOpDataReaderListener<DataUnion>
    {
    public:
        Listener(CallbackMap &callbacks, std::mutex &mutex)
            : callbacks_(callbacks), mutex_(mutex)
        {
        }

        void on_data_available(dds::sub::DataReader<DataUnion> &reader) override
        {
            dds::sub::LoanedSamples<DataUnion> samples = reader.take();
            for (const auto &sample : samples)
            {
                if (sample.info().valid())
                {
                    const auto &data = sample.data();
                    std::lock_guard<std::mutex> lock(mutex_);
                    if (auto it = callbacks_.find(data._d());
                        it != callbacks_.end())
                    {
                        it->second(data);
                    }
                }
            }
        }

    private:
        CallbackMap &callbacks_;
        std::mutex &mutex_;
    };

    dds::domain::DomainParticipant participant_;
    dds::sub::Subscriber subscriber_;
    dds::topic::Topic<DataUnion> topic_;
    dds::sub::DataReader<DataUnion> reader_;
    std::unique_ptr<Listener> listener_;

    std::mutex mutex_;
    CallbackMap callbacks_;
};

// ==================== 通用Publisher ====================
template <typename DataUnion,
          typename TopicEnum,
          typename Traits = DdsTypeTraits<DataUnion, TopicEnum>>
class UnionPublisher : base::Noncopyable
{
public:
    static DdsResult<std::shared_ptr<UnionPublisher>> Create(
        uint32_t domain_id,
        const std::string &topic_name)
    {
        try
        {
            dds::domain::DomainParticipant participant(domain_id);
            dds::pub::Publisher publisher(participant);
            dds::topic::Topic<DataUnion> topic(participant, topic_name);
            dds::pub::DataWriter<DataUnion> writer(publisher, topic);

            auto *instance = new UnionPublisher(std::move(participant),
                                                  std::move(publisher),
                                                  std::move(topic),
                                                  std::move(writer));
            return DdsResult<std::shared_ptr<UnionPublisher>>::Success(
                std::shared_ptr<UnionPublisher>(instance));
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<std::shared_ptr<UnionPublisher>>::Failure(
                "DDS error: " + std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<std::shared_ptr<UnionPublisher>>::Failure(
                "System error: " + std::string(e.what()));
        }
    }

    template <typename T>
    DdsResult<bool> Publish(const T &msg)
    {
        static_assert(IsSupportedType<T>(), "Unsupported message type");

        DataUnion data;
        try
        {
            Traits::template set_value<T>(data, msg);
            writer_.write(data);
            return DdsResult<bool>::Success(true);
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<bool>::Failure("DDS error: " +
                                            std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<bool>::Failure("System error: " +
                                            std::string(e.what()));
        }
    }

    DdsResult<bool> IsConnected()
    {
        try
        {
            auto status = writer_.publication_matched_status();
            return DdsResult<bool>::Success(status.current_count() > 0);
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<bool>::Failure("DDS error: " +
                                            std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<bool>::Failure("System error: " +
                                            std::string(e.what()));
        }
    }

    DdsResult<int> GetMatchedSubscribersCount()
    {
        try
        {
            auto status = writer_.publication_matched_status();
            return DdsResult<int>::Success(status.current_count());
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<int>::Failure("DDS error: " +
                                           std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<int>::Failure("System error: " +
                                           std::string(e.what()));
        }
    }

private:
    template <typename T>
    static constexpr bool IsSupportedType()
    {
        // return std::is_same_v<T,
        //                       decltype(Traits::template get_value<T>(
        //                           std::declval<DataUnion>()))>;
        using ReturnType = decltype(Traits::template get_value<T>(std::declval<DataUnion>()));
        // 移除引用和cv限定符进行比较
        return std::is_same_v<std::decay_t<ReturnType>, std::decay_t<T>>;
    }

    UnionPublisher(dds::domain::DomainParticipant &&participant,
                     dds::pub::Publisher &&publisher,
                     dds::topic::Topic<DataUnion> &&topic,
                     dds::pub::DataWriter<DataUnion> &&writer)
        : participant_(std::move(participant)),
          publisher_(std::move(publisher)), topic_(std::move(topic)),
          writer_(std::move(writer))
    {
    }

    dds::domain::DomainParticipant participant_;
    dds::pub::Publisher publisher_;
    dds::topic::Topic<DataUnion> topic_;
    dds::pub::DataWriter<DataUnion> writer_;
};

} // namespace dds_wrapper
} // namespace fotamaster
} // namespace seres