#pragma once

#include "OTA_DucData.hpp"
#include "base/noncopyable.h"
#include "dds/dds.hpp"
#include "duc_service_client.h"
#include "logger/logger.h"
#include "topic_subscriber.h"
using namespace seres::ota_duc_service;

namespace seres
{
namespace fotamaster
{


// 将DUCType转换为字符串
std::string ducTypeToString(DUCType type);
std::string returnCodeToString(ReturnCode code);

// 话题消息回调函数类型定义
using InventoryResultCallback = std::function<void(const InventoryResult &)>;
using DownloadProgressCallback = std::function<void(const DownloadProgress &)>;
using UzipPackagesResultCallback =
    std::function<void(const UzipPackagesResult &)>;
using PackagesVerifyResultCallback =
    std::function<void(const PackagesVerifyResult &)>;
using CheckUpdateConditionResultCallback =
    std::function<void(const CheckUpdateConditionResult &)>;
using UpdateProgressCallback = std::function<void(const UpdateProgress &)>;
using RollbackProgressCallback = std::function<void(const UpdateProgress &)>;


class DUCServiceManager : base::Noncopyable
{
public:
    // 单例模式获取实例
    DUCServiceManager();
    ~DUCServiceManager();

    bool initialize(int domainId = 0);
    bool createClient(DUCType type,
                      std::string serviceName = "",
                      ServiceStatusCallback statusCallback = nullptr);
    // 根据DUC类型获取客户端
    DucServiceInterfaceClient *getDUCClient(DUCType type);


    // 话题订阅相关方法
    bool subscribeInventoryResult(DUCType type,
                                  const InventoryResultCallback &callback);
    bool subscribeDownloadProgress(DUCType type,
                                   const DownloadProgressCallback &callback);
    bool subscribeUpdateProgress(DUCType type,
                                 const UpdateProgressCallback &callback);
    bool subscribeUzipPackagesResult(
        DUCType type,
        const UzipPackagesResultCallback &callback);
    bool subscribePackagesVerifyResult(
        DUCType type,
        const PackagesVerifyResultCallback &callback);
    bool subscribeCheckUpdateConditionResult(
        DUCType type,
        const CheckUpdateConditionResultCallback &callback);


    // 资产信息获取相关方法
    ReturnCode inventoryCollection(DUCType type,
                                   const SelectedInventoryList &inventoryList);
    ReturnCode stopInventoryCollection(DUCType type);
    ReturnCode getInventoryResult(DUCType type, InventoryResult &result);

    // 下载前检查相关方法
    ReturnCode checkDownloadCondition(DUCType type,
                                      const DownloadConditionLists &conditions,
                                      DownloadConditionResult &result);

    // 下载相关方法
    ReturnCode startDownload(DUCType type, const DownloadTaskLists &tasks);
    ReturnCode downloadCtrl(DUCType type, DownloadCtrl command);
    ReturnCode getDownloadProgress(DUCType type,
                                   DownloadProgress &downloadProgress);

    // 解压相关方法
    ReturnCode uzipPackages(DUCType type);
    ReturnCode getuzipPackagesResult(DUCType type,
                                     UzipPackagesResult &uzipResult);

    // 包验证相关方法
    ReturnCode startPackagesVerify(DUCType type);
    ReturnCode getPackagesVerifyResult(DUCType type,
                                       PackagesVerifyResult &verifyResult);

    // 升级相关方法
    ReturnCode checkUpdateCondition(DUCType type);
    ReturnCode getCheckUpdateConditionResult(
        DUCType type,
        CheckUpdateConditionResult &checkConditionResult);
    ReturnCode startUpdate(DUCType type,
                           UpdateMode mode,
                           const UpdateDeviceList &updateList);
    ReturnCode resumeUpdate(DUCType type);
    ReturnCode pauseUpdate(DUCType type);
    ReturnCode getUpdateProgress(DUCType type, UpdateProgress &updateProgress);

    // 激活方法
    ReturnCode activate(DUCType type);

    // 回滚相关方法
    ReturnCode rollback(DUCType type, const RollbackComponentList &components);
    ReturnCode getRollbackProgress(DUCType type,
                                   UpdateProgress &rollbackProgress);

    // 日志上传方法
    ReturnCode uploadLog(DUCType type);

    // 停止所有服务
    void shutdown();

private:
    bool checkClientExist(DUCType type);
    bool checkClientConnected(DUCType type);
    std::string getTopicName(DUCType type);
    std::string getServiceName(DUCType type);
    void onTopicDataReceived(DUCType type, const OTA_DucDataUnion &data);

    // DDS参与者
    std::shared_ptr<dds::domain::DomainParticipant> m_participant;
    //DDS订阅者
    std::shared_ptr<dds::sub::Subscriber> m_subscriber;
    // 客户端实例映射表
    std::unordered_map<DUCType, std::unique_ptr<DUCServiceClient>> m_clients;

    //
    std::unordered_map<DUCType,
                       std::unique_ptr<TopicSubscriber<OTA_DucDataUnion>>>
        m_TopicSubscribers;
    std::unordered_map<DUCType, InventoryResultCallback>
        m_InventoryResultCallbacks;
    std::unordered_map<DUCType, DownloadProgressCallback>
        m_DownloadProgressCallbacks;
    std::unordered_map<DUCType, UzipPackagesResultCallback>
        m_UzipPackagesResultCallbacks;
    std::unordered_map<DUCType, PackagesVerifyResultCallback>
        m_PackagesVerifyResultCallbacks;
    std::unordered_map<DUCType, CheckUpdateConditionResultCallback>
        m_CheckUpdateConditionResultCallbacks;
    std::unordered_map<DUCType, UpdateProgressCallback>
        m_UpdateProgressCallbacks;

    // 互斥锁
    std::mutex m_mutex;

    // 初始化标志
    bool m_initialized;
};

} // namespace fotamaster
} // namespace seres