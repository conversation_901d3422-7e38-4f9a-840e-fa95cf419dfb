#pragma once

#include "OTA_VucServiceData.hpp"
#include "dds_service_manager/dds_wapper.hpp"

namespace seres
{
namespace fotamaster
{
namespace dds_wrapper
{
using namespace seres::ota_vuc_service;

template <>
struct DdsTypeTraits<VucServiceDataUnion, VucServiceTopic>
{
    template <typename T>
    static constexpr auto type_to_enum()
    {
        if constexpr (std::is_same_v<T, InventoryInfoList>)
            return VucServiceTopic::kReportInventoryInfo;
        else if constexpr (std::is_same_v<T, SeamlessUpgradeMode>)
            return VucServiceTopic::kSeamlessUpgradeMode;
        else if constexpr (std::is_same_v<T, UpgradeTask>)
            return VucServiceTopic::kUpgradeTaskNotify;
        else if constexpr (std::is_same_v<T, TotalDownloadProgress>)
            return VucServiceTopic::kReportDownloadProgress;
        else if constexpr (std::is_same_v<T, DownloadFailReason>)
            return VucServiceTopic::kReportDownloadFailReason;
        else if constexpr (std::is_same_v<T, UpgradeModeCtrl>)
            return VucServiceTopic::kUpgradeModeCtrl;
        else if constexpr (std::is_same_v<T, UpgradeCountdown>)
            return VucServiceTopic::kRequestUpgradeCountdown;
        else if constexpr (std::is_same_v<T, UpgradeNotify>)
            return VucServiceTopic::kUpgradeNotify;
        else if constexpr (std::is_same_v<T, UpgradeProgress>)
            return VucServiceTopic::kReportUpgradeProgress;
        else
            static_assert(always_false_v<T>, "Unsupported type");
    }

    template <typename T>
    static const auto &get_value(const VucServiceDataUnion &data)
    {
        if constexpr (std::is_same_v<T, InventoryInfoList>)
            return data.inventory_info_list();
        else if constexpr (std::is_same_v<T, SeamlessUpgradeMode>)
            return data.seamless_upgrade_mode();
        else if constexpr (std::is_same_v<T, UpgradeTask>)
            return data.upgrade_task();
        else if constexpr (std::is_same_v<T, TotalDownloadProgress>)
            return data.downlaod_progress();
        else if constexpr (std::is_same_v<T, DownloadFailReason>)
            return data.download_fail_reason();
        else if constexpr (std::is_same_v<T, UpgradeModeCtrl>)
            return data.upgrade_mode_ctrl();
        else if constexpr (std::is_same_v<T, UpgradeCountdown>)
            return data.upgrade_countdown();
        else if constexpr (std::is_same_v<T, UpgradeNotify>)
            return data.upgrade_notify();
        else if constexpr (std::is_same_v<T, UpgradeProgress>)
            return data.upgrade_progress();
        else
            static_assert(always_false_v<T>, "Unsupported type");
    }

    template <typename T>
    static void set_value(VucServiceDataUnion &data, const T &value)
    {
        if constexpr (std::is_same_v<T, InventoryInfoList>)
            data.inventory_info_list(value);
        else if constexpr (std::is_same_v<T, SeamlessUpgradeMode>)
            data.seamless_upgrade_mode(value);
        else if constexpr (std::is_same_v<T, UpgradeTask>)
            data.upgrade_task(value);
        else if constexpr (std::is_same_v<T, TotalDownloadProgress>)
            data.downlaod_progress(value);
        else if constexpr (std::is_same_v<T, DownloadFailReason>)
            data.download_fail_reason(value);
        else if constexpr (std::is_same_v<T, UpgradeModeCtrl>)
            data.upgrade_mode_ctrl(value);
        else if constexpr (std::is_same_v<T, UpgradeCountdown>)
            data.upgrade_countdown(value);
        else if constexpr (std::is_same_v<T, UpgradeNotify>)
            data.upgrade_notify(value);
        else if constexpr (std::is_same_v<T, UpgradeProgress>)
            data.upgrade_progress(value);
        else
            static_assert(always_false_v<T>, "Unsupported type");
    }
private:
    template <class T>
    static constexpr bool always_false_v = false;
};

using VucSubscriber = UnionSubscriber<VucServiceDataUnion, VucServiceTopic>;
using VucPublisher = UnionPublisher<VucServiceDataUnion, VucServiceTopic>;

} // namespace dds_union_wrapper
} // namespace fotamaster
} // namespace seres