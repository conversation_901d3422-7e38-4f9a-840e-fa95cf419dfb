#
# Copyright(c) 2020 to 2022 ZettaScale Technology and others
#
# This program and the accompanying materials are made available under the
# terms of the Eclipse Public License v. 2.0 which is available at
# http://www.eclipse.org/legal/epl-2.0, or the Eclipse Distribution License
# v. 1.0 which is available at
# http://www.eclipse.org/org/documents/edl-v10.php.
#
# SPDX-License-Identifier: EPL-2.0 OR BSD-3-Clause
#
cmake_minimum_required(VERSION 3.16)

if (CMAKE_BINARY_DIR STREQUAL CMAKE_CURRENT_SOURCE_DIR)
  message(FATAL_ERROR "Building in-source is not supported. "
                      "Create a build dir and remove CMakeFiles and CMakeCache.txt")
endif()

message("cmake version: ${CMAKE_MAJOR_VERSION}.${CMAKE_MINOR_VERSION}")
if(${CMAKE_VERSION} VERSION_LESS 3.16)
    cmake_policy(VERSION ${CMAKE_MAJOR_VERSION}.${CMAKE_MINOR_VERSION})
else()
    cmake_policy(VERSION 3.16)
endif()

project(fotamaster VERSION 1.0
    DESCRIPTION "ota_vuc or fotamaster"
    LANGUAGES C CXX)

list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}/cmake")
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}/../cdds_install/x64/lib/cmake")

if(NOT TARGET CycloneDDS-CXX::ddscxx)
  find_package(CycloneDDS-CXX REQUIRED)
endif()

find_package(Threads REQUIRED)
find_package(spdlog REQUIRED)
find_package(libev REQUIRED)

message("CycloneDDS-CXX_FOUND: ${CycloneDDS-CXX_FOUND}")
message("spdlog_FOUND: ${spdlog_FOUND}")

idlcxx_generate(TARGET ota_service
  FILES
    gen/rpcCommon.idl
    gen/OTA_DucInterface.idl
    gen/OTA_DucData.idl
    gen/OTA_VucServiceData.idl
  WARNINGS
    no-implicit-extensibility)

include(GNUInstallDirs)
include(CheckAndAddFiles)
check_and_add_files(${PROJECT_NAME}_SRC
  "src/"
    init.cpp
    device_config.cpp
    download_manager.cpp
    inventory_manager.cpp
    upgrade_manager.cpp
    fota_master_manager.cpp
    main.cpp
)

check_and_add_files(${PROJECT_NAME}_base_SRC
  "src/base/"
    threadinfo.cpp
)

check_and_add_files(${PROJECT_NAME}_event_SRC
  "src/ev_loop"
    eventloop.cpp
    eventloop_manager.cpp
    timeout_handler.cpp
    timer.cpp
    signal_handler.cpp
    signal_manager.cpp
)

check_and_add_files(${PROJECT_NAME}_logger_SRC
  "src/logger"
    logger_config.cpp
    logger.cpp
)

check_and_add_files(${PROJECT_NAME}_state_SRC
  "src/state_machine"
    ota_state_machine.cpp
    download_state.cpp
    exit_state.cpp
    fault_state.cpp
    formal_upgrade_state.cpp
    idle_state.cpp
    pre_formal_upgrade_state.cpp
    rollback_state.cpp
    seamless_upgrade_finish_state.cpp
    seamless_upgrade_state.cpp
)

check_and_add_files(${PROJECT_NAME}_gen_SRC
  "gen/"
    rpc/OTA_DucInterface_gen_client.cpp
)

check_and_add_files(${PROJECT_NAME}_progress_fitting_SRC
  "src/progress_fitting/"
    progress_fitting.cpp
)

check_and_add_files(${PROJECT_NAME}_dds_manager_SRC
  "src/dds_service_manager"
    duc_service_manager.cpp
    duc_service_client.cpp
)

add_executable(${PROJECT_NAME}
    ${${PROJECT_NAME}_base_SRC}
    ${${PROJECT_NAME}_logger_SRC}
    ${${PROJECT_NAME}_event_SRC}
    ${${PROJECT_NAME}_state_SRC}
    ${${PROJECT_NAME}_SRC}
    ${${PROJECT_NAME}_gen_SRC}
    ${${PROJECT_NAME}_dds_manager_SRC}
    ${${PROJECT_NAME}_progress_fitting_SRC}
)

set_target_properties(${PROJECT_NAME}
  PROPERTIES
    # set CXX Standard to c++17
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    ${cyclonedds_cpp_std_to_use}
)

target_include_directories(${PROJECT_NAME}
  PRIVATE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/gen>
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../cdds_install/x64/include/rpcddscxx>
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../cdds_install/x64/include>
    $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>
)

target_compile_options(${PROJECT_NAME}
  PRIVATE
    -Wall -Wextra -pedantic
    # -Weffc++
    $<$<CONFIG:Debug>:-Wsign-conversion -ggdb>
    $<$<CONFIG:Release>:-Wno-unused-variable>
)

target_link_directories(${PROJECT_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/../cdds_install/x64/lib)
target_link_libraries(${PROJECT_NAME}
  PRIVATE
    Threads::Threads
    CycloneDDS-CXX::ddscxx
    rpcddscxx
    libev::libev
    spdlog::spdlog
    ota_service
)

# 传递变量, 使用target_compile_definitions
target_compile_definitions(${PROJECT_NAME} PRIVATE OTA_CFG_FILE_PATH_PERFIX="${CMAKE_INSTALL_PREFIX}/cfg/${PROJECT_NAME}")

# install
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin
)

install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/cfg/
    DESTINATION ${CMAKE_INSTALL_PREFIX}/cfg/${PROJECT_NAME}
)

# only build examples if this is the main project
if(CMAKE_PROJECT_NAME STREQUAL PROJECT_NAME)
    option(ENABLE_TEST "define the enable test for component, default OFF" OFF)
    option(ENABLE_ASAN "define the enable asan for component, default OFF" OFF)
endif()

if (ENABLE_TEST)
    add_subdirectory(unittest)
endif()