#include "inventory_manager.h"
#include "logger/logger.h"
#include <algorithm>
#include <cassert>

namespace seres
{
namespace fotamaster
{

InventoryManager::InventoryManager(DUCServiceManager *duc_service_manager)
    : duc_srv_manager_(duc_service_manager)
{
    assert(duc_srv_manager_);

    std::string dev_config_path =
        OTA_CFG_FILE_PATH_PERFIX + std::string("/ota_device_lists.json");
    LOG_INFO("dev_config_path: %s", dev_config_path.c_str());
    DeviceListRecoderConfig dev_config(std::move(dev_config_path));
    auto retval = dev_config.GetDeviceListConfig(all_dev_list_);
    assert(retval);

    assert(Initialize());
}

bool InventoryManager::TriggerInventoryCollection()
{
    SelectedInventoryList inventoryList;
    for (const auto &device_name : all_dev_list_.cdc_dev_list)
    {
        LOG_INFO("CDC device: %s", device_name.c_str());
        inventoryList.inventoryLists().push_back(device_name);
    }
    auto retval = StartInventoryCollection(DUCType::CDC, inventoryList);
    // TODO 添加MDC和ZCU的资产收集
    return retval;
}

bool InventoryManager::StopInventoryCollection()
{
    auto retval = duc_srv_manager_->stopInventoryCollection(DUCType::CDC);
    LOG_INFO("stopInventoryCollection invoke retval: %d",
             static_cast<int>(retval));
    return (retval == ReturnCode::OK) ? true : false;
}

bool InventoryManager::Initialize()
{
    auto retval = duc_srv_manager_->subscribeInventoryResult(
        DUCType::CDC,
        [this](const InventoryResult &result) -> void {
            LOG_INFO("Recv CDC inventory result:");
            for (const auto &info : result.InventoryLists())
            {
                LOG_INFO("  ECU: %s, version: %s",
                         info.ecuName().c_str(),
                         info.softwareVersion().c_str());
            }
            if (cdc_try_count_.load(std::memory_order_acquire) > 0)
            {
                if (!CheckInventory(DUCType::CDC, result))
                {
                    cdc_try_count_.fetch_sub(1, std::memory_order_release);
                }
                else
                {
                    LOG_INFO("CDC inventory collection finished.");
                    cdc_try_count_.store(0, std::memory_order_release);
                }
            }
        });
    // TODO 监听MDC和ZCU的资产收集回调
    return retval;
}

bool InventoryManager::StartInventoryCollection(
    DUCType type,
    const SelectedInventoryList &inventory_list)
{
    auto retval = duc_srv_manager_->inventoryCollection(type, inventory_list);
    LOG_INFO("inventoryCollection invoke retval: %d", static_cast<int>(retval));
    return (retval == ReturnCode::OK) ? true : false;
}

bool InventoryManager::CheckInventory(DUCType type, const InventoryResult &result)
{
    // 先将收集到的资产信息存入缓存

    auto diff = CheckInventoryResult(all_dev_list_.cdc_dev_list, result);
    if (diff.has_value())
    {
        // 还有资产没有获取到,继续获取
        auto dev_list = diff.value();
        SelectedInventoryList inventoryList;
        for (const auto &device_name : dev_list)
        {
            LOG_INFO("Retry CDC device: %s", device_name.c_str());
            inventoryList.inventoryLists().push_back(device_name);
        }
        StartInventoryCollection(DUCType::CDC, inventoryList);
        return false;
    }
    return true;
}

auto InventoryManager::CheckInventoryResult(
    const std::vector<std::string> &dev_list,
    const InventoryResult &result) -> std::optional<std::vector<std::string>>
{
    std::optional<std::vector<std::string>> retval{std::nullopt};
    auto inventory_list = result.InventoryLists();
    LOG_INFO("dev_list size: %lu, inventory_list size: %lu",
        dev_list.size(), inventory_list.size());
    if (inventory_list.size() != dev_list.size())
    {
        std::vector<std::string> dev_names;
        dev_names.reserve(inventory_list.size());
        for (const auto &inventory : inventory_list)
        {
            dev_names.push_back(inventory.ecuName());
        }
        std::sort(dev_names.begin(), dev_names.end());

        std::vector<std::string> diff;
        // 计算对称差集（仅存在于一个容器中的元素）
        std::set_symmetric_difference(dev_list.begin(),
                                      dev_list.end(),
                                      dev_names.begin(),
                                      dev_names.end(),
                                      std::back_inserter(diff));
        if (diff.size() > 0)
        {
            LOG_INFO("diff dev list size: %lu", diff.size());
            return std::optional<std::vector<std::string>>{diff};
        }
    }
    return retval;
}

void InventoryManager::SaveInventoryResult(const InventoryResult &result)
{
    seres::ota_vuc_service::ComponentInfo component_info;
    // TODO 赋值
    // component_info.partNumber(result.InventoryLists);
    std::lock_guard<std::mutex> lock_{inventory_info_list_mutex_};
    inventory_info_list_.componentLists().push_back(component_info);
}

} // namespace fotamaster
} // namespace seres