#include "download_manager.h"
#include "logger/logger.h"
#include <cassert>

namespace seres
{
namespace fotamaster
{

DownloadManager::DownloadManager(DUCServiceManager *duc_service_manager)
    : duc_srv_manager_{duc_service_manager}
{
    // 注册下载进度回调
    auto retval = duc_srv_manager_->subscribeDownloadProgress(
        DUCType::CDC,
        [this](const DownloadProgress &progress) -> void {
            LOG_INFO("recv download progress info:");
            for (const auto &info : progress.progressLists())
            {
                LOG_INFO("  package name: %s, progress: %d%%, state: %d",
                         info.packageName().c_str(),
                         info.progressPercent(),
                         static_cast<int>(info.status()));
                if (progress_callback_)
                {
                    TotalDownloadProgress total_progress;
                    total_progress.progress(info.progressPercent());
                    progress_callback_(total_progress);
                }
            }

            // TDOD 进度拟合
        });
    assert(retval);
}

bool DownloadManager::CheckDownloadCondition()
{
    DownloadConditionLists downloadConditions;
    DownloadRequirement downloadReq;
    downloadReq.deviceId("dv001");
    downloadReq.diskRequirement(500);
    downloadConditions.downloadRequirementLists().push_back(downloadReq);

    DownloadConditionResult cond_result;
    auto retval = duc_srv_manager_->checkDownloadCondition(DUCType::CDC,
                                                           downloadConditions,
                                                           cond_result);
    if (retval != ReturnCode::OK)
    {
        LOG_ERROR("checkDownloadCondition invoke failed");
        return false;
    }

    if (cond_result != DownloadConditionResult::NOEXCEPTION)
    {
        LOG_ERROR("Check download condition failed");
        if (fail_reason_callback_)
        {
            DownloadFailReason fail_reason;
            if (cond_result == DownloadConditionResult::DISK_NOT_ENOUGH)
            {
                fail_reason = DownloadFailReason::kDiskSpaceNotEnough;
            }
            else if (cond_result == DownloadConditionResult::NetworkERROR)
            {
                fail_reason = DownloadFailReason::kNetworkAnomaly;
            }
            else if (cond_result == DownloadConditionResult::PARAMS_INVALID)
            {
                fail_reason = DownloadFailReason::kIllegalArgs;
            }
            else
            {
                LOG_INFO("Unkown Download condition result: %d",
                         static_cast<int32_t>(cond_result));
            }
            fail_reason_callback_(fail_reason);
        }
        return false;
    }

    LOG_INFO("Check download condition succeed");

    return true;
}

bool DownloadManager::ProgressFitting(const DownloadProgress &progress)
{
    return true;
}

bool DownloadManager::StartDownload()
{
    DownloadTaskLists tasks;
    DownloadTaskInfo task;
    task.taskId("TASK001");
    task.packageVersion("1.0.0");
    task.packageName("test_package");
    task.packageUrl("http://test.com/package");
    task.packageSize("1024");
    task.packageMd5("md5sum");
    tasks.taskLists().push_back(task);

    auto retval = duc_srv_manager_->startDownload(DUCType::CDC, tasks);
    LOG_INFO("startDownload invoke retval: %d", static_cast<int>(retval));
    return (retval == ReturnCode::OK) ? true : false;
}

bool DownloadManager::StopDownload()
{
    auto retval =
        duc_srv_manager_->downloadCtrl(DUCType::CDC, DownloadCtrl::PAUSE);
    LOG_INFO("downloadCtrl stop invoke retval: %d", static_cast<int>(retval));
    return (retval == ReturnCode::OK) ? true : false;
}

bool DownloadManager::ResumeDownload()
{
    auto retval =
        duc_srv_manager_->downloadCtrl(DUCType::CDC, DownloadCtrl::RESUME);
    LOG_INFO("downloadCtrl resume invoke retval: %d", static_cast<int>(retval));
    return (retval == ReturnCode::OK) ? true : false;
}

bool DownloadManager::CancelDownload()
{
    auto retval =
        duc_srv_manager_->downloadCtrl(DUCType::CDC, DownloadCtrl::CANCEL);
    LOG_INFO("downloadCtrl cancel invoke retval: %d", static_cast<int>(retval));
    return (retval == ReturnCode::OK) ? true : false;
}

} // namespace fotamaster
} // namespace seres