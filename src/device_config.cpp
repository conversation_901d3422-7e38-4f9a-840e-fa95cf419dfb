#include "device_config.h"
#include <cassert>
#include <fstream>
#include <filesystem>
#include <algorithm>

namespace seres
{
namespace fotamaster
{

DeviceListRecoderConfig::DeviceListRecoderConfig(std::string &&config_path)
    : config_path_{std::move(config_path)}
{
    assert(std::filesystem::exists(config_path_));
}

bool DeviceListRecoderConfig::GetDeviceListConfig(ns::DeviceListConfig& dev_config)
{
    std::ifstream file(config_path_, std::ios::in | std::ios::binary);
    if (!file.is_open())
    {
        return false;
    }

    ns::Json j;
    file >> j;

    j.at(ns::kDevListsConfigLabel).get_to(dev_config);
    std::sort(dev_config.cdc_dev_list.begin(), dev_config.cdc_dev_list.end());
    std::sort(dev_config.mdc_dev_list.begin(), dev_config.mdc_dev_list.end());
    std::sort(dev_config.zcu_dev_list.begin(), dev_config.zcu_dev_list.end());

    file.close();
    return true;
}

} // namespace fotamaster
} // namespace seres