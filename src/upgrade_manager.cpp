#include "upgrade_manager.h"
#include "dds_service_manager/duc_service_manager.h"
#include <cassert>
#include "logger/logger.h"

namespace seres
{
namespace fotamaster
{

UpgradeManager::UpgradeManager(DUCServiceManager *duc_service_manager)
    : duc_srv_manager_{duc_service_manager}
{
    assert(duc_srv_manager_);
    // 注册下载进度回调
    auto retval = duc_srv_manager_->subscribeUpdateProgress(DUCType::CDC,
        [this](const UpdateProgress& progress) -> void {
            LOG_INFO("recv upgrade progress:");
            for (const auto& info : progress.progressLists()) {
                LOG_INFO("  device: %s, progress: %d%%, status: %d",
                    info.deviceName().c_str(),
                    info.progressPercent(),
                    static_cast<int>(info.status()));
            }
            // 进度拟合
        });

    retval = duc_srv_manager_->subscribeCheckUpdateConditionResult(DUCType::CDC,
        [this](const CheckUpdateConditionResult &result) -> void {
            LOG_INFO("recv check condition result: Passed=%d, Error code=%d",
                result.passed(),
                static_cast<int>(result.errorCode()));
        });
    assert(retval);
}

bool UpgradeManager::CheckUpgradeCondition()
{
    // 更新条件检查
    auto retval = duc_srv_manager_->checkUpdateCondition(DUCType::CDC);
    LOG_INFO("checkUpdateCondition invoke retval: %d", static_cast<int>(retval));
    return (retval == ReturnCode::OK) ? true : false;
}

void UpgradeManager::ProgressFitting()
{

}

bool UpgradeManager::StartUpgrade(const UpgradeMode &upgrade_mode)
{
    UpdateMode mode = UpdateMode::FormalMode;
    if (upgrade_mode == UpgradeMode::kSeamlessUpgrade)
    {
        mode = UpdateMode::SeamlessMode;
    }

    UpdateDeviceList updateList;
    InventoryInfo updateInfo;
    updateInfo.partNumber("TEST001");
    updateInfo.softwareVersion("1.0.0");
    updateInfo.supplierCode("SUP001");
    updateInfo.ecuName("TestECU");
    updateList.updateDeviceLists().push_back(updateInfo);

    auto retval = duc_srv_manager_->startUpdate(DUCType::CDC, mode, updateList);
    LOG_INFO("startUpdate invoke retval: %d", static_cast<int>(retval));
    return (retval == ReturnCode::OK) ? true : false;
}

}
}