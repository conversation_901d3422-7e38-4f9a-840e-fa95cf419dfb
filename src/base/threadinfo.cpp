#include "base/threadinfo.h"
#include <pthread.h>
#include <unistd.h>
#include <sys/syscall.h>   /* For SYS_xxx definitions */
#include <type_traits>

namespace seres
{
namespace fotamaster
{
namespace base
{

namespace curthread
{

__thread int t_cachedTid = 0;
static_assert(std::is_same<int, pid_t>::value, "pid_t should be int");

static pid_t gettid()
{
    return static_cast<pid_t>(::syscall(SYS_gettid));
}

void CacheTid()
{
    if (t_cachedTid == 0)
    {
        t_cachedTid = gettid();
    }
}

bool is_main_thread()
{
    return tid() == ::getpid();
}

bool set_thread_name(pthread_t thread_handle, const std::string &name)
{
    std::string n = name;
    if (n.size() > 15)
        n.resize(15);
    return pthread_setname_np(thread_handle, n.c_str()) == 0;
}

std::string get_thread_name(pthread_t thread_handle)
{
    char thread_name[256];
    auto rc = pthread_getname_np(thread_handle, thread_name, sizeof(thread_name));
    if (rc != 0)
    {
        return std::string("unknown_thread");
    }
    else
    {
        return std::string(thread_name);
    }
}

} // namespace curthread

} // namespace base
} // namespace fotamaster
} // namespace seres
