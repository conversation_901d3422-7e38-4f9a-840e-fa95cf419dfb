#include "ev_loop/signal_handler.h"
#include "ev.h"
#include <cassert>

#include "base/singleton.h"
#include "ev_loop/eventloop.h"
#include "ev_loop/eventloop_manager.h"

namespace seres
{
namespace fotamaster
{

void SignalHandler::SignalCallback(struct ev_loop *backend,
                                   struct ev_signal *watcher,
                                   int)
{
    EventLoop *loop = reinterpret_cast<EventLoop *>(ev_userdata(backend));
    SignalHandler *self = reinterpret_cast<SignalHandler *>(watcher->data);

    assert(self->loop_ == loop);
    self->HandleSignal(watcher->signum);
}

SignalHandler::SignalHandler(EventLoop *loop) : loop_(loop), signal_watchers_()
{
    assert(loop &&
           loop ==
               base::Singleton<EventLoopManager>::Instance().GetDefaultLoop());
}

SignalHandler::~SignalHandler()
{
    for (auto &handler : signal_watchers_)
    {
        ev_signal_stop(loop_->GetEventLoopBackend(), handler.second.get());
    }
    signal_watchers_.clear();
}

std::optional<ErrCode> SignalHandler::RegisterSignal(int signal) noexcept
{
    assert(loop_->IsInLoopThread());

    if (signal_watchers_.count(signal) > 0)
    {
        return std::optional<ErrCode>{ErrCode::kRegisteredEvent};
    }

    std::unique_ptr<ev_signal> watcher(new (std::nothrow) ev_signal);
    assert(watcher);
    auto result = signal_watchers_.emplace(signal, std::move(watcher));
    assert(result.second);
    auto *handler = result.first->second.get();
    handler->data = reinterpret_cast<void *>(this);
    ev_signal_init(handler, &SignalHandler::SignalCallback, signal);
    ev_signal_start(loop_->GetEventLoopBackend(), handler);

    return std::optional<ErrCode>{};
}

void SignalHandler::UnregisterSignal(int signal) noexcept
{
    assert(loop_->IsInLoopThread());

    auto iter = signal_watchers_.find(signal);
    if (iter == signal_watchers_.cend())
    {
        return;
    }

    ev_signal_stop(loop_->GetEventLoopBackend(), iter->second.get());
    signal_watchers_.erase(iter);
}

} // namespace fotamaster
} // namespace seres
