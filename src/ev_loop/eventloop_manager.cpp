#include "ev_loop/eventloop_manager.h"
#include <cassert>
#include <sstream>

namespace seres
{
namespace fotamaster
{
EventLoopManager::EventLoopManager()
{
    std::stringstream ss;

    ss << std::this_thread::get_id();
    default_loop_name_ = ss.str();
    auto loop = new EventLoop{default_loop_name_, true};
    auto result =
        loops_.emplace(default_loop_name_, std::unique_ptr<EventLoop>(loop));
    assert(result.second);
    (void)result;
}

EventLoopManager::~EventLoopManager()
{
    // StopAllEventLoop();
    std::unordered_map<std::string, std::unique_ptr<EventLoop>> loops;
    {
        std::lock_guard<std::mutex> lock{loops_mutex_};
        loops.swap(loops_);
    }

    loops.clear();
}

EventLoop *EventLoopManager::GetDefaultLoop()
{
    std::lock_guard<std::mutex> lock{loops_mutex_};
    assert(loops_.count(default_loop_name_) > 0);

    return loops_[default_loop_name_].get();
}

EventLoop *EventLoopManager::GetThreadLoop(std::thread::id thread_id)
{
    std::stringstream ss;

    if (thread_id == std::thread::id())
    {
        thread_id = std::this_thread::get_id();
    }

    ss << thread_id;
    auto name = ss.str();

    std::lock_guard<std::mutex> lock{loops_mutex_};
    auto iter = loops_.find(name);
    if (iter == loops_.cend())
    {
        auto loop = new EventLoop{name};
        auto result =
            loops_.emplace(std::move(name), std::unique_ptr<EventLoop>(loop));
        assert(result.second == true);
        iter = result.first;
    }

    return iter->second.get();
}

} // namespace fotamaster
} // namespace seres
