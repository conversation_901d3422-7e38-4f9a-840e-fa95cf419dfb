#include "base/singleton.h"
#include "ev_loop/eventloop_manager.h"
#include "ev_loop/signal_manager.h"
#include "logger/logger.h"
#include "fota_master_manager.h"
#include <chrono>
#include <signal.h>
#include <thread>
#include <string>
#include <memory>

using namespace seres::fotamaster;

static void logger_init()
{
#if 0
    // 配置日志系统
    LogConfig config;
    config.log_file_path = "logs/ota_master.log"; // 日志文件路径
    config.max_file_size = 5 * 1024 * 1024;       // 5MB
    config.max_files = 3;                         // 最多保留3个文件
    config.console_output = true;                 // 输出到控制台
    config.log_level = LogLevel::kLogLevelDebug;  // 日志级别

    // 初始化日志系统
    base::Singleton<Logger>::Instance().Init(config);
#else
    std::string configRealpath = OTA_CFG_FILE_PATH_PERFIX + std::string("/logger_config.json");
    // 初始化日志系统
    base::Singleton<Logger>::Instance().Init(std::move(configRealpath));
#endif
}

static void signal_handler(EventLoop *loop)
{
    base::Singleton<SignalManager>::Instance(loop).RegisterHandler(
        SIGTERM,
        [loop](int) {
            LOG_INFO("Process SIGTERM signal ,terminate ota master node...");
            loop->TerminateLoop();
        });

    base::Singleton<SignalManager>::Instance(loop).RegisterHandler(
        SIGINT,
        [loop](int) {
            LOG_INFO("Process SIGINT signal ,terminate ota master node...");
            loop->TerminateLoop();
        });
}

int main(int argc, char *argv[])
{
    (void)argc;
    (void)argv;

    // 初始化日志
    logger_init();

    auto loop = base::Singleton<EventLoopManager>::Instance().GetDefaultLoop();
    assert(loop);

    signal_handler(loop);

    auto fota_master_manager = std::make_unique<FOTAMasterManager>(loop);
    assert(fota_master_manager);
    // 上电开始资产收集
    fota_master_manager->TriggerInventoryCollection();

    loop->LoopForever();
    return 0;
}