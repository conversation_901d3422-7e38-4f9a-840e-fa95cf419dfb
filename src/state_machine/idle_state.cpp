#include "state_machine/idle_state.h"
#include "logger/logger.h"
namespace seres
{
namespace fotamaster
{

bool IdleState::Enter()
{
    LOG_INFO("=====Enter idle state");
    return true;
}

void IdleState::Process(const EventData &data)
{
    LOG_INFO("=====Process idle state");
    HandleRequestEvent(std::any_cast<OtaInState>(data.data));
}

void IdleState::Exit()
{
    LOG_INFO("=====Exit idle state");
}

void IdleState::HandleRequestEvent(const OtaInState& ota_in_state)
{
    if (ota_in_state == OtaInState::kOtaInDownloadState)
    {
        GetStateMachine()->ChangeState("DownloadState");
    }
}

REGISTER_STATE("IdleState", IdleState)

} // namespace fotamaster
} // namespace seres