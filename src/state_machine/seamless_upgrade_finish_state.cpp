#include "state_machine/seamless_upgrade_finish_state.h"
#include "logger/logger.h"
#include "state_machine/ota_state_machine.h"

namespace seres
{
namespace fotamaster
{

bool SeamlessUpgradeFinishState::Enter()
{
    LOG_INFO("=====Enter seamless upgrade state finish state");
    return true;
}

void SeamlessUpgradeFinishState::Process(const EventData &data)
{
    LOG_INFO("=====Process seamless upgrade finish state");
    OtaInState ota_in_state = std::any_cast<OtaInState>(data.data);
    if (ota_in_state == OtaInState::kOtaInPreFormalUpgradeState)
    {
        GetStateMachine()->ChangeState("PreFormalUpgradeState");
    }
}

void SeamlessUpgradeFinishState::Exit()
{
    LOG_INFO("=====Exit seamless upgrade finish state");
}

REGISTER_STATE("SeamlessUpgradeFinishState", SeamlessUpgradeFinishState)

} // namespace fotamaster
} // namespace seres