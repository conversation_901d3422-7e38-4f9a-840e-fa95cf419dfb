#include "state_machine/rollback_state.h"
#include "state_machine/ota_state_machine.h"
#include "logger/logger.h"

namespace seres
{
namespace fotamaster
{

bool RollbackState::Enter()
{
    LOG_INFO("===Enter rollback state");
    if (!GetStateMachine()->EventCallback(StateEvent::kStartRollback))
    {
        LOG_ERROR("Call start rollback failed");
        return false;
    }
    return true;
}

void RollbackState::Process(const EventData &data)
{
    LOG_INFO("=====Process rollback state");
    OtaInState ota_in_state = std::any_cast<OtaInState>(data.data);
    if (ota_in_state == OtaInState::kOtaInFaultState)
    {
        GetStateMachine()->ChangeState("FaultState");
    }
}

void RollbackState::Exit()
{
    LOG_INFO("===Exit rollback state");
}

REGISTER_STATE("RollbackState", RollbackState)

} // namespace fotamaster
} // namespace seres