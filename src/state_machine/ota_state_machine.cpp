#include "state_machine/ota_state_machine.h"
#include "OTA_DucData.hpp"
#include "base/classfactory.h"
#include "base/singleton.h"
#include "logger/logger.h"
#include <vector>

namespace seres
{
namespace fotamaster
{

OtaStateMachine::OtaStateMachine(EventLoop *event_loop)
    : StateMachine(event_loop)
{
}

OtaStateMachine::~OtaStateMachine()
{
}

void OtaStateMachine::InitState()
{
    auto state_name_vec = base::Singleton<base::ClassFactoryManager>::Instance()
                              .GetRegisteredClasses<State>();
    LOG_INFO("Registered states size: %lu", state_name_vec.size());

    for (const auto &state_name : state_name_vec)
    {
        LOG_INFO("Machine add state: %s", state_name.c_str());
        AddState(state_name, this);
    }
}

void OtaStateMachine::ChangeState(const std::string &new_state)
{
    if ((GetCurrentState() == new_state))
    {
        LOG_WARN("request same state, no change!!!");
        return;
    }

    StateMachine::ChangeState(new_state);
}

void OtaStateMachine::HandleEvent(const OtaStateMsg &ota_info)
{
    if (ota_info.in_state == ota_state_msg_.in_state)
    {
        LOG_INFO("request same state, no handle!!!");
        return;
    }

    ota_state_msg_ = ota_info;

    EventData event_data;
    event_data.data = ota_info.in_state;
    GetState(GetCurrentState())->Process(event_data);
}

bool OtaStateMachine::EventCallback(const StateEvent &state,
                                    const std::any &data)
{
    if (!event_cb_)
    {
        LOG_ERROR("event callback not registered");
        return false;
    }
    return event_cb_(state, data);
}

} // namespace fotamaster
} // namespace seres