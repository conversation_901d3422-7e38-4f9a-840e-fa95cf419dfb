name: linux

on: [push, pull_request]

permissions:
  contents: read

jobs:
  # -----------------------------------------------------------------------
  # Linux build matrix
  # -----------------------------------------------------------------------
  build:
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    strategy:
      fail-fast: false
      matrix:
        config:
          - { compiler: gcc, version: 7, build_type: Release, cppstd: 11 }
          - { compiler: gcc, version: 9, build_type: Release, cppstd: 17 }
          - { compiler: gcc, version: 11, build_type: Debug, cppstd: 20 }
          - { compiler: gcc, version: 12, build_type: Release, cppstd: 20 }
          - { compiler: gcc, version: 12, build_type: Debug, cppstd: 20, asan: ON }
          - { compiler: clang, version: 12, build_type: Debug, cppstd: 17 }
          - { compiler: clang, version: 15, build_type: Release, cppstd: 20, tsan: ON }
    container:
      image: ${{ matrix.config.compiler == 'clang' && 'teeks99/clang-ubuntu' || matrix.config.compiler }}:${{ matrix.config.version }}
    name: "${{ matrix.config.compiler}} ${{ matrix.config.version }} (C++${{ matrix.config.cppstd }} ${{ matrix.config.build_type }} ${{ matrix.config.asan == 'ON' && 'ASAN' || '' }}${{ matrix.config.tsan == 'ON' && 'TSAN' || '' }})"
    steps:
      - uses: actions/checkout@v4
      - name: Setup
        run: |
          apt-get update
          apt-get install -y curl git pkg-config libsystemd-dev
          CMAKE_VERSION="3.24.2"
          curl -sSL https://github.com/Kitware/CMake/releases/download/v${CMAKE_VERSION}/cmake-${CMAKE_VERSION}-linux-x86_64.sh -o install-cmake.sh
          chmod +x install-cmake.sh
          ./install-cmake.sh --prefix=/usr/local --skip-license
      - name: Setup Compiler
        if: matrix.config.compiler == 'clang'
        run: |
          scripts/ci_setup_clang.sh "${{ matrix.config.version }}"
          echo "CXXFLAGS=-stdlib=libc++" >> $GITHUB_ENV          
          echo "CC=clang-${{ matrix.config.version }}" >> $GITHUB_ENV
          echo "CXX=clang++-${{ matrix.config.version }}" >> $GITHUB_ENV
      - name: Build
        run: |
          mkdir -p build && cd build
          cmake .. \
            -DCMAKE_BUILD_TYPE=${{ matrix.config.build_type }} \
            -DCMAKE_CXX_STANDARD=${{ matrix.config.cppstd }} \
            -DSPDLOG_BUILD_EXAMPLE=${{ matrix.config.examples || 'ON' }} \
            -DSPDLOG_BUILD_EXAMPLE_HO=${{ matrix.config.examples || 'ON' }} \
            -DSPDLOG_BUILD_WARNINGS=ON \
            -DSPDLOG_BUILD_BENCH=OFF \
            -DSPDLOG_BUILD_TESTS=ON \
            -DSPDLOG_BUILD_TESTS_HO=OFF \
            -DSPDLOG_SANITIZE_ADDRESS=${{ matrix.config.asan || 'OFF' }} \
            -DSPDLOG_SANITIZE_THREAD=${{ matrix.config.tsan || 'OFF' }}
          make -j 4
          ctest -j 4 --output-on-failure

  # -----------------------------------------------------------------------
  # OS X build matrix
  # -----------------------------------------------------------------------
  build_osx:
    runs-on: macOS-latest
    name: "OS X Clang (C++11, Release)"
    steps:
      - uses: actions/checkout@v4
      - name: Build
        run: |
          mkdir -p build && cd build
          cmake .. \
            -DCMAKE_BUILD_TYPE=Release \
            -DCMAKE_CXX_STANDARD=11 \
            -DSPDLOG_BUILD_EXAMPLE=ON \
            -DSPDLOG_BUILD_EXAMPLE_HO=ON \
            -DSPDLOG_BUILD_WARNINGS=ON \
            -DSPDLOG_BUILD_BENCH=OFF \
            -DSPDLOG_BUILD_TESTS=ON \
            -DSPDLOG_BUILD_TESTS_HO=OFF \
            -DSPDLOG_SANITIZE_ADDRESS=OFF
          make -j 4
          ctest -j 4 --output-on-failure
