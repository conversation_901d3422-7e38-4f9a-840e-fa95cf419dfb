name: windows

on: [push, pull_request]

permissions:
  contents: read

jobs:
  build:
    runs-on: windows-latest
    strategy:
      fail-fast: true
      matrix:
        config:
          - GENERATOR: "Visual Studio 17 2022"
            BUILD_TYPE: Release
            BUILD_SHARED: 'ON'
            FATAL_ERRORS: 'ON'
            WCHAR: 'OFF'
            WCHAR_FILES: 'OFF'
            BUILD_EXAMPLE: 'OFF'
            USE_STD_FORMAT: 'ON'
            CXX_STANDARD: 20
          - GENERATOR: "Visual Studio 17 2022"
            BUILD_TYPE: Release
            BUILD_SHARED: 'ON'
            FATAL_ERRORS: 'ON'
            WCHAR: 'ON'
            WCHAR_FILES: 'ON'
            BUILD_EXAMPLE: 'OFF'
            USE_STD_FORMAT: 'ON'
            CXX_STANDARD: 20
          - GENERATOR: "Visual Studio 17 2022"
            BUILD_TYPE: Release
            BUILD_SHARED: 'ON'
            FATAL_ERRORS: 'ON'
            WCHAR: 'OFF'
            WCHAR_FILES: 'OFF'
            BUILD_EXAMPLE: 'ON'
            USE_STD_FORMAT: 'OFF'
            CXX_STANDARD: 17

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: CMake ${{ matrix.config.GENERATOR }} CXX=${{matrix.config.CXX_STANDARD}} WCHAR=${{matrix.config.WCHAR_FILES}} STD_FORMAT=${{matrix.config.USE_STD_FORMAT}}
        shell: pwsh
        run: |
          mkdir build
          cd build
          cmake -G "${{ matrix.config.GENERATOR }}"  -A x64 `
            -D CMAKE_BUILD_TYPE=${{ matrix.config.BUILD_TYPE }} `
            -D BUILD_SHARED_LIBS=${{ matrix.config.BUILD_SHARED }} `
            -D SPDLOG_WCHAR_SUPPORT=${{ matrix.config.WCHAR }} `
            -D SPDLOG_WCHAR_FILENAMES=${{ matrix.config.WCHAR_FILES }} `
            -D SPDLOG_BUILD_EXAMPLE=${{ matrix.config.BUILD_EXAMPLE }} `
            -D SPDLOG_BUILD_EXAMPLE_HO=${{ matrix.config.BUILD_EXAMPLE }} `
            -D SPDLOG_BUILD_TESTS=ON `
            -D SPDLOG_BUILD_TESTS_HO=OFF `
            -D SPDLOG_BUILD_WARNINGS=${{ matrix.config.FATAL_ERRORS }} `
            -D SPDLOG_USE_STD_FORMAT=${{ matrix.config.USE_STD_FORMAT }} `
            -D CMAKE_CXX_STANDARD=${{ matrix.config.CXX_STANDARD }} ..

      - name: Build
        shell: pwsh
        run: |
          cd build
          cmake --build  . --parallel --config ${{ matrix.config.BUILD_TYPE }}

      - name: Run Tests
        shell: pwsh
        env:
          PATH: ${{ env.PATH }};${{ github.workspace }}\build\_deps\catch2-build\src\${{ matrix.config.BUILD_TYPE }};${{ github.workspace }}\build\${{ matrix.config.BUILD_TYPE }}
        run: |
          build\tests\${{ matrix.config.BUILD_TYPE }}\spdlog-utests.exe

  # -----------------------------------------------------------------------
  # MSVC 2019 build matrix
  # -----------------------------------------------------------------------
  build_2019:
    runs-on: windows-2019
    strategy:
      fail-fast: true
      matrix:
        config:
          - GENERATOR: "Visual Studio 16 2019"
            BUILD_TYPE: Release
            BUILD_SHARED: 'ON'
            FATAL_ERRORS: 'ON'
            WCHAR: 'OFF'
            WCHAR_FILES: 'OFF'
            BUILD_EXAMPLE: 'ON'
            USE_STD_FORMAT: 'OFF'
            CXX_STANDARD: 17
          - GENERATOR: "Visual Studio 16 2019"
            BUILD_TYPE: Release
            BUILD_SHARED: 'ON'
            FATAL_ERRORS: 'ON'
            WCHAR: 'OFF'
            WCHAR_FILES: 'OFF'
            BUILD_EXAMPLE: 'ON'
            USE_STD_FORMAT: 'OFF'
            CXX_STANDARD: 14
          - GENERATOR: "Visual Studio 16 2019"
            BUILD_TYPE: Release
            BUILD_SHARED: 'ON'
            FATAL_ERRORS: 'ON'
            WCHAR: 'OFF'
            WCHAR_FILES: 'OFF'
            BUILD_EXAMPLE: 'ON'
            USE_STD_FORMAT: 'OFF'
            CXX_STANDARD: 11

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: CMake ${{ matrix.config.GENERATOR }} CXX=${{matrix.config.CXX_STANDARD}} WCHAR=${{matrix.config.WCHAR_FILES}} STD_FORMAT=${{matrix.config.USE_STD_FORMAT}}
        shell: pwsh
        run: |
          mkdir build
          cd build
          cmake -G "${{ matrix.config.GENERATOR }}"  -A x64 `
            -D CMAKE_BUILD_TYPE=${{ matrix.config.BUILD_TYPE }} `
            -D BUILD_SHARED_LIBS=${{ matrix.config.BUILD_SHARED }} `
            -D SPDLOG_WCHAR_SUPPORT=${{ matrix.config.WCHAR }} `
            -D SPDLOG_WCHAR_FILENAMES=${{ matrix.config.WCHAR_FILES }} `
            -D SPDLOG_BUILD_EXAMPLE=${{ matrix.config.BUILD_EXAMPLE }} `
            -D SPDLOG_BUILD_EXAMPLE_HO=${{ matrix.config.BUILD_EXAMPLE }} `
            -D SPDLOG_BUILD_TESTS=ON `
            -D SPDLOG_BUILD_TESTS_HO=OFF `
            -D SPDLOG_BUILD_WARNINGS=${{ matrix.config.FATAL_ERRORS }} `
            -D SPDLOG_USE_STD_FORMAT=${{ matrix.config.USE_STD_FORMAT }} `
            -D CMAKE_CXX_STANDARD=${{ matrix.config.CXX_STANDARD }} ..

      - name: Build
        shell: pwsh
        run: |
          cd build
          cmake --build  . --parallel --config ${{ matrix.config.BUILD_TYPE }}

      - name: Run Tests
        shell: pwsh
        env:
          PATH: ${{ env.PATH }};${{ github.workspace }}\build\_deps\catch2-build\src\${{ matrix.config.BUILD_TYPE }};${{ github.workspace }}\build\${{ matrix.config.BUILD_TYPE }}
        run: |
          build\tests\${{ matrix.config.BUILD_TYPE }}\spdlog-utests.exe
