version: 1.0.{build}
image: Visual Studio 2017
environment:
  matrix:
    - GENERATOR: '"Visual Studio 15 2017 Win64"'
      BUILD_TYPE: Debug
      BUILD_SHARED: 'OFF'
      FATAL_ERRORS: 'OFF'
      WCHAR: 'ON'
      WCHAR_FILES: 'OFF'
      BUILD_EXAMPLE: 'ON'
      USE_STD_FORMAT: 'OFF'
      CXX_STANDARD: 11
    - GENERATOR: '"Visual Studio 15 2017 Win64"'
      BUILD_TYPE: Release
      BUILD_SHARED: 'OFF'
      FATAL_ERRORS: 'OFF'
      WCHAR: 'OFF'
      WCHAR_FILES: 'OFF'
      BUILD_EXAMPLE: 'ON'
      USE_STD_FORMAT: 'OFF'
      CXX_STANDARD: 11
    - GENERATOR: '"Visual Studio 15 2017 Win64"'
      BUILD_TYPE: Release
      BUILD_SHARED: 'ON'
      FATAL_ERRORS: 'OFF'
      WCHAR: 'OFF'
      WCHAR_FILES: 'OFF'
      BUILD_EXAMPLE: 'ON'
      USE_STD_FORMAT: 'OFF'
      CXX_STANDARD: 11
    - GENERATOR: '"Visual Studio 15 2017 Win64"'
      BUILD_TYPE: Release
      BUILD_SHARED: 'ON'
      FATAL_ERRORS: 'OFF'
      WCHAR: 'ON'
      WCHAR_FILES: 'ON'
      BUILD_EXAMPLE: 'OFF'
      USE_STD_FORMAT: 'OFF'
      CXX_STANDARD: 11
    - GENERATOR: '"Visual Studio 16 2019" -A x64'
      BUILD_TYPE: Release
      BUILD_SHARED: 'ON'
      FATAL_ERRORS: 'ON'
      WCHAR: 'OFF'
      WCHAR_FILES: 'OFF'
      BUILD_EXAMPLE: 'OFF'
      USE_STD_FORMAT: 'OFF'
      CXX_STANDARD: 17
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
    - GENERATOR: '"Visual Studio 17 2022" -A x64'
      BUILD_TYPE: Release
      BUILD_SHARED: 'ON'
      FATAL_ERRORS: 'ON'
      WCHAR: 'OFF'
      WCHAR_FILES: 'OFF'
      BUILD_EXAMPLE: 'OFF'
      USE_STD_FORMAT: 'ON'
      CXX_STANDARD: 20
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2022
    - GENERATOR: '"Visual Studio 17 2022" -A x64'
      BUILD_TYPE: Release
      BUILD_SHARED: 'ON'
      FATAL_ERRORS: 'ON'
      WCHAR: 'ON'
      WCHAR_FILES: 'ON'
      BUILD_EXAMPLE: 'OFF'
      USE_STD_FORMAT: 'ON'
      CXX_STANDARD: 20
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2022
build_script:
  - cmd: >-
      set

      mkdir build

      cd build

      set PATH=%PATH%;C:\Program Files\Git\usr\bin

      cmake -G %GENERATOR% -D CMAKE_BUILD_TYPE=%BUILD_TYPE% -D BUILD_SHARED_LIBS=%BUILD_SHARED% -D SPDLOG_WCHAR_SUPPORT=%WCHAR% -D SPDLOG_WCHAR_FILENAMES=%WCHAR_FILES% -D SPDLOG_BUILD_EXAMPLE=%BUILD_EXAMPLE% -D SPDLOG_BUILD_EXAMPLE_HO=%BUILD_EXAMPLE% -D SPDLOG_BUILD_TESTS=ON -D SPDLOG_BUILD_TESTS_HO=OFF -D SPDLOG_BUILD_WARNINGS=%FATAL_ERRORS% -D SPDLOG_USE_STD_FORMAT=%USE_STD_FORMAT% -D CMAKE_CXX_STANDARD=%CXX_STANDARD% ..

      cmake --build . --config %BUILD_TYPE%

before_test:
  - set PATH=%PATH%;C:\projects\spdlog\build\_deps\catch2-build\src\%BUILD_TYPE%;C:\projects\spdlog\build\%BUILD_TYPE%
  
test_script:
  - C:\projects\spdlog\build\tests\%BUILD_TYPE%\spdlog-utests.exe
